const getHttp = () => uni.$u.http;

// 学校数据空间列表
// shareType:
// 2等同0 能看到这个空间但是看不到空间里面有什么东西
// 1 能看到这个空间和里面有什么东西 (比如对一个父空间的子空间赋予了权限,但是没有给父空间赋予权限
// 不传 什么都看不到
// 后端逻辑是只要一个空间的shareType为1,就会递归所有子级都变为1,所以测试的时候要把所有父级空间都变为2才能出现shareType返回值为2的情况
export function getSpaceFileList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/folder/subListPages', params);
}
// 我的数据空间列表
export function getMySpaceFileList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/subListPage', params);
}

// 删除学校-文件/文件夹
export function mixBatchDelete(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/folder/mixBatchDelete', params);
}

// 删除我的-文件/文件夹
export function myMixBatchDelete(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/mixBatchDelete', params);
}

// 删除学校数据空间
export function deleteSpaceFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/delete', params);
}

// 回收站列表
export function getRecycleBinFileList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/page', params);
}

// 判断回收站文件父空间是否存在
export function hasParent(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/hasParent', params);
}

// 批量判断回收站文件父空间是否存在(有一个不存在就会返回false)
export function batchHasParent(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/batchHasParent', params);
}

// 回收站恢复
export function recoveryRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/recovery', params);
}

// 回收站批量恢复
export function batchRecoveryRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/batchRecovery', params);
}

// 回收站彻底删除
export function deleteRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/delete', params);
}

// 回收站批量彻底删除
export function batchDeleteRecycleBinFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/recycle/batchDelete', params);
}

// 上传到学校/我的数据空间
export function uploadToSpace(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/upload', params);
}

// 文件上传埋点
export function reportCloudFileUpload(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/client/activity/report/cloud/file/upload', params);
}

// 获取指定应用列表(思维导图,文档解读)
export function getAppointedAppList(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/client/app/center/appointedAppList', params);
}

// 从我的空间选择文件添加到学校数据空间
export function addFromMyself(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/addFromMyself', params);
}

// 新增文件夹
export function addFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/add', params);
}

// 重命名文件
export function renameFile(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/rename', params);
}

// 重命名文件夹
export function renameFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/folder/rename', params);
}

// 被移动的是文件
export function updateParent(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/file/updateParent', params);
}

// 被移动的是文件夹
export function updateParentFolder(params: any): Promise<any> {
  return getHttp().post('/huayun-ai/app/cloud/space/updateParent', params);
}
