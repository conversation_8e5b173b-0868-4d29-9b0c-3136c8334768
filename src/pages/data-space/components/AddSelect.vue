<script lang="ts" setup>
import { defineOptions, ref, defineEmits, nextTick } from 'vue';
import { getBaseUrl } from '@/common/ai/url';
import { addFolder } from '@/api/database';
import { uploadFileWithResume } from '@/utils/uploadWithResume';
import LkSvg from '@/components/svg/index.vue';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';

interface ToolItem {
  name: string;
  icon: string;
  type: string;
}

interface ModelValue {
  title?: string;
  type?: 'subAdd' | 'add';
  bizType?: string;
  parentId?: string;
}

const defaultToolsValue = [
  {
    name: '本地文件',
    icon: 'localFile',
    type: 'localFile',
  },
  // uniapp的app环境中不支持上传微信文件，所以暂时不显示
  // {
  //   name: '微信文件',
  //   icon: 'wechat',
  //   type: 'wechatFile',
  // },
];

const props = defineProps<{
  bizType: string;
  parentId: string;
}>();

const emit = defineEmits(['update:modelValue', 'refreshPageList']);

const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();
const modelValue = ref<ModelValue>({});
const toolList = ref<ToolItem[]>([...defaultToolsValue]);
// 上传组件
const xeUploadRef = ref<any>(null);
const uploadOptions = ref({});
const isAddFolder = ref(false);
const folderName = ref('');
const toolMap = {
  subAdd: [
    {
      name: '我的空间',
      icon: 'space',
      type: 'mySpace',
    },
  ],
  add: [
    {
      name: '文件夹',
      icon: 'folder',
      type: 'folder',
    },
  ],
};

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const onOpen = (value: ModelValue) => {
  folderName.value = '';
  show.value = true;
  modelValue.value = value;

  const selectedTool = toolMap[value?.type || 'add'];
  toolList.value = [...toolList.value, ...selectedTool] as ToolItem[];
};

const onClose = () => {
  isAddFolder.value = false;
  show.value = false;
  modelValue.value = {};
  toolList.value = [...defaultToolsValue];
};

const handleSearch = () => {
  console.log('handleSearch', folderName.value);
};

const placeholderStyle = 'color: #86909C; font-size: 14px;';

const handleAddFolder = () => {
  console.log('handleAddFolder', folderName.value);
  addFolder({
    folderName: folderName.value,
    parentId: props.parentId,
  }).then(res => {
    console.log('res', res);
    emit('refreshPageList');
    onClose();
  });
};

// 添加一个更可靠的云端登记函数
function cloudRegister(fileKey: string, bizType: string, parentId: string, uniqueFileId: string) {
  const baseUrl = getBaseUrl();
  console.log('尝试云端登记:', fileKey, bizType, parentId);

  // 初始设置为99%，表示正在云端登记
  updateRecordProgress(uniqueFileId, 99);

  uni.request({
    url: `${baseUrl}/huayun-ai/cloud/file/upload`, // 确保使用完整URL
    method: 'POST',
    header: {
      Authorization: uni.getStorageSync('token'),
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: {
      fileKey: fileKey,
      bizType: Number(bizType),
      folderId: parentId,
      isAuditRoot: 1,
    },
    success: (res: any) => {
      console.log('云端登记响应:', res);
      if (res.statusCode === 200 && res.data && res.data.code === 200) {
        // 明确设置为100%
        updateRecordStatus(uniqueFileId, 'success', 100);
        emit('refreshPageList');
        uni.showToast({
          title: '上传成功',
          icon: 'success',
        });
        onClose();
      } else {
        console.error('云端登记接口失败:', res);
        uni.showToast({
          title: (res.data && res.data.message) || '云端登记失败',
          icon: 'none',
        });
        updateRecordStatus(uniqueFileId, 'failed');
      }
    },
    fail: err => {
      console.error('云端登记请求失败:', err);
      uni.showToast({ title: '云端登记请求失败', icon: 'none' });
      updateRecordStatus(uniqueFileId, 'failed');

      // 添加重试选项
      uni.showModal({
        title: '上传提示',
        content: '文件已上传，但云端登记失败，是否重试？',
        success: function (res) {
          if (res.confirm) {
            // 重试云端登记
            cloudRegister(fileKey, bizType, parentId, uniqueFileId);
          }
        },
      });
    },
  });
}

const CHUNK_SIZE_THRESHOLD = 3 * 1024 * 1024; // 文件达到3MB则使用分片上传
// const CHUNK_SIZE_THRESHOLD = 0.1 * 1024 * 1024; // 文件达到0.1MB则使用分片上传

function handleUploadCallback(e: any) {
  if (e.type === 'choose') {
    console.log('选择的文件:', e.data);
    // 兼容数组和单文件
    if (Array.isArray(e.data)) {
      e.data.forEach((file: any) => uploadToServer(file, 'file'));
    } else {
      uploadToServer(e.data, 'file');
    }
  } else if (e.type === 'warning') {
    console.error('文件上传警告:', e.data);
    uni.showToast({ title: e.data, icon: 'none' });
  }
}

function uploadToServer(file: any, type: string) {
  const uniqueFileId =
    file && file.uid ? file.uid : (file && file.name ? file.name : 'file') + '_' + Date.now();
  file.uniqueId = uniqueFileId; // 【关键修正】确保 file 带有 uniqueId
  const bizType = props.bizType;
  const parentId = props.parentId;
  const onProgress = (progress: number) => {
    let records = uni.getStorageSync('uploadRecords') || [];
    const idx = records.findIndex((r: any) => r.uniqueId === uniqueFileId);
    if (idx !== -1) {
      console.log(`上传进度更新: ${uniqueFileId} - ${progress}%`);
      // 确保有最小进度1%，避免显示为0%
      progress = Math.max(progress, 1);
      records[idx].progress = progress;
      records[idx].status = progress === 100 ? 'success' : 'uploading';
      uni.setStorageSync('uploadRecords', records);
      uni.$emit('uploadRecordsUpdated');
    }
  };

  // 记录初始化
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!records.find((r: any) => r.uniqueId === uniqueFileId)) {
    records.push({
      uniqueId: uniqueFileId,
      fileName: file.name,
      fileSize: file.size,
      status: 'uploading',
      progress: 0,
      uploadTime: Date.now(),
      bizType,
      parentId,
      file: file,
      fileKey: '',
    });
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }

  // 分片上传
  uploadFileWithResume(file, bizType, parentId, onProgress)
    .then(() => {
      // 分片上传成功后，执行原直传成功回调中的核心逻辑
      // 1. 更新进度为99
      let records = uni.getStorageSync('uploadRecords') || [];
      if (!Array.isArray(records)) records = [];
      const idx = records.findIndex((r: any) => r.uniqueId === uniqueFileId);
      if (idx !== -1 && records[idx].progress < 99) {
        updateRecordProgress(uniqueFileId, 99);
      }

      // 2. 使用新的云端登记函数，替代原来的uploadFile
      const recordItem = records[idx];
      if (recordItem && recordItem.fileKey) {
        cloudRegister(recordItem.fileKey, bizType, parentId, uniqueFileId);
      } else {
        console.error('缺少fileKey，无法进行云端登记');
        uni.showToast({ title: '上传信息不完整，请重试', icon: 'none' });
        updateRecordStatus(uniqueFileId, 'failed');
      }
    })
    .catch(err => {
      let records = uni.getStorageSync('uploadRecords') || [];
      const idx = records.findIndex((r: any) => r.uniqueId === uniqueFileId);
      if (idx !== -1) {
        records[idx].status = 'failed';
        uni.setStorageSync('uploadRecords', records);
        uni.$emit('uploadRecordsUpdated');
      }
      console.error('分片上传失败', err);
    });
}

// --- 辅助函数 ---
function updateRecordStatus(uniqueId: string, status: string, progress?: number) {
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === uniqueId);
  if (idx !== -1) {
    records[idx].status = status;
    if (typeof progress !== 'undefined') {
      records[idx].progress = progress;
    }
    // 确保失败时进度也合理显示，例如不清零或设为特定值
    if (status === 'failed' && typeof progress === 'undefined') {
      // records[idx].progress = 0; // 或者保持当前进度
    }
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }
}

function updateRecordProgress(uniqueId: string, progress: number) {
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === uniqueId);
  if (idx !== -1) {
    if (records[idx].status === 'uploading') {
      const safeProgress = progress === 100 ? 100 : Math.min(progress, 99);
      if (safeProgress > records[idx].progress) {
        records[idx].progress = safeProgress;
        uni.setStorageSync('uploadRecords', records);
        uni.$emit('uploadRecordsUpdated');
      }
    }
  }
}

// 第一个接口 (/public/upload) 成功后调用
function updateRecordAfterFirstUpload(uniqueId: string, firstUploadData: any) {
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === uniqueId);
  if (idx !== -1) {
    records[idx].fileUrl = firstUploadData.fileUrl;
    records[idx].fileKey = firstUploadData.fileKey;
    records[idx].id = firstUploadData.id; // 假设id由第一个接口返回
    // 状态和进度保持不变 (仍然是 'uploading' 和当前进度)
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }
}

const handleClickItem = (item: ToolItem) => {
  console.log(item);
  if (item.type === 'localFile') {
    console.log('本地文件');
    xeUploadRef.value.upload('file', {});
  } else if (item.type === 'wechatFile') {
    console.log('微信文件');
  } else if (item.type === 'mySpace') {
    console.log('我的空间');
    lkDatabasePopupRef.value?.openPopup();
  } else if (item.type === 'folder') {
    console.log('文件夹');
    isAddFolder.value = true;
  }
};

// 对外暴露直接打开本地文件上传的方法
const openLocalFileUpload = () => {
  // 打开弹窗
  show.value = true;

  // 设置弹窗标题和类型
  modelValue.value = {
    title: props.bizType === '1' ? '学校数据空间' : '我的数据空间',
    type: props.bizType === '1' ? 'subAdd' : 'add',
  };

  // 初始化工具列表
  const selectedTool = toolMap[modelValue.value.type || 'add'];
  toolList.value = [...defaultToolsValue, ...selectedTool] as ToolItem[];

  // 短暂延迟后触发点击本地文件按钮
  setTimeout(() => {
    if (xeUploadRef.value) {
      // 模拟点击"本地文件"选项
      xeUploadRef.value.upload('file', {});
    }
  }, 100);
};

defineExpose({ onOpen, onClose, openLocalFileUpload });
defineOptions({ name: 'AddSelect' });
</script>

<template>
  <up-popup :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="add-select safe-area-inset-bottom" v-if="!isAddFolder">
      <view class="add-select__header flex-center">
        <LkText bold>添加</LkText>
        <LkText type="tertiary" size="small">添加至 {{ modelValue?.title }}</LkText>
      </view>
      <view class="wrapClose">
        <LkSvg src="/static/database/close.svg" width="24px" height="24px" @tap="onClose" />
      </view>
      <view class="add-select__list">
        <template v-for="item in toolList" :key="item.type">
          <view class="add-select__list-item flex-center" @tap="handleClickItem(item)">
            <LkSvg :src="`/static/database/${item.icon}.svg`" width="40px" height="40px" />
            <LkText size="small">{{ item.name }}</LkText>
          </view>
        </template>
      </view>
      <!-- 如果遇到无法唤醒且报错[wxs]："module XeUpload not found"时尝试调整层级,好像是不能和popup处于同级 -->
      <xe-upload
        ref="xeUploadRef"
        :options="uploadOptions"
        @callback="handleUploadCallback"
      ></xe-upload>
    </view>
    <view class="addFolder" v-else>
      <view class="title">新增文件夹</view>
      <view class="wrapClose">
        <LkSvg src="/static/database/close.svg" width="24px" height="24px" @tap="onClose" />
      </view>
      <view class="wrapContent">
        <view class="title">
          <view class="txt">文件夹名称</view>
          <LkSvg class="required" src="/static/database/required.svg" width="8px" height="8px" />
        </view>
        <view class="wrapInput">
          <up-input
            v-model="folderName"
            :show-action="false"
            placeholder="请输入"
            @search="handleSearch"
            @confirm="handleSearch"
            border="none"
            :placeholderStyle="placeholderStyle"
            clearable
          ></up-input>
        </view>
        <view class="wrapBtn">
          <LkButton :disabled="!folderName" type="primary" shape="round" @tap="handleAddFolder"
            >确定</LkButton
          >
        </view>
      </view>
    </view>
  </up-popup>
  <LkDatabasePopup ref="lkDatabasePopupRef" bizType="2" :layoutType="2" />
</template>

<style lang="scss" scoped>
.add-select {
  background: #fff;
  border-radius: 14px 14px 0 0;
  min-height: 30vh;
  padding: 18px;
  padding-top: 16px;
  padding-bottom: 10px;
  .wrapClose {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  &__header {
    gap: 5px;
    flex-direction: column;
  }
  &__list {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    &-item {
      flex-direction: column;
      gap: 7px;
      padding: 10px;
      border-radius: 14px;
      background: white;
      flex: 1;
      .lk-text {
        color: #1d2129;
        font-size: 30rpx;
        white-space: nowrap;
      }
    }
  }
}
.addFolder {
  background: #fff;
  border-radius: 14px 14px 0 0;
  min-height: 30vh;
  padding: 18px;
  padding-top: 16px;
  padding-bottom: 14px;
  position: relative;
  > .title {
    font-size: 18px;
    font-weight: 600;
    color: #1d2129;
    text-align: center;
  }
  .wrapClose {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  .wrapContent {
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #1d2129;
      margin-top: 14px;
      .txt {
      }
      .required {
        margin-left: 6px;
      }
    }
  }
  .wrapInput {
    display: flex;
    height: 48px;
    align-items: center;
    border-radius: 8px;
    background: #f2f3f5;
    margin-top: 10px;
    padding: 0 16px;
    .u-input {
      font-size: 14px;
    }
  }
  .wrapBtn {
    margin-top: 104px;
    .lk-button {
      width: 100%;
    }
  }
}
</style>
