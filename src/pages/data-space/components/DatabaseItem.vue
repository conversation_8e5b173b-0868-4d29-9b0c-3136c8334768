<script setup lang="ts">
import LkSvg from '@/components/svg/index.vue';
import {
  getSpaceFileList,
  getMySpaceFileList,
  mixBatchDelete,
  myMixBatchDelete,
  deleteSpaceFile,
} from '@/api/database';
import { ref, watch } from 'vue';
import FileViewer from '@/components/LkFileViewer/index.vue';
import OptionTool from './OptionTool.vue';
import OptionShare from './OptionShare.vue';
import OptionDel from './OptionDel.vue';
import LkPageList from '@/components/LkPageList/index.vue';
import NoData from './NoData.vue';
import AddSelect from './AddSelect.vue';
import { useUserStore } from '@/store/userStore';

const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);

const emit = defineEmits(['goToMySpace', 'guide']);

const props = defineProps({
  isCreator: {
    type: Boolean,
    default: false,
  },
  isFileInfo: {
    type: Boolean,
    default: false,
  },
  pathList: {
    type: Array as () => any[],
    required: true,
    default: () => [],
  },
  bizType: {
    type: String,
    default: '1',
  },
});

const fileProps = ref({
  fileUrl: '',
  fileType: '',
});
const fileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

// 使用 LkPageList 替代原有的加载逻辑
const fetchSpaceFileList = async (params: any) => {
  const data = await (props.bizType === '1' ? getSpaceFileList : getMySpaceFileList)({
    ...params,
  });
  emit('guide', data.records);
  return data;
};

const isNoData = ref(false);
const processData = (data: any) => {
  console.log(data);
  if (data.length === 0) {
    isNoData.value = true;
  }
  return data;
};

const optionsList = [
  {
    style: {
      backgroundColor: '#A6A6A6',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/5849fcbac6a1721968c83830dfb3f622.svg',
  },
  {
    style: {
      backgroundColor: '#8B8B8B',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/8122eec722aa08581f49a2b32a27317a.svg',
  },
  {
    style: {
      backgroundColor: '#D54941',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
  },
];

const getOptionsByItem = (item: any) => {
  if (item.fileType === 1) {
    return optionsList;
  } else if (item.fileType !== 1) {
    return [optionsList[0], optionsList[2]];
  }
};

const optionTool = ref<InstanceType<typeof OptionTool>>();
const optionShare = ref<InstanceType<typeof OptionShare>>();
const optionDel = ref<InstanceType<typeof OptionDel>>();
const lkPageListRef = ref<InstanceType<typeof LkPageList>>();

const clickOption = (obj: any, item: any) => {
  console.log('222222', obj, item);
  if (obj.index === 0) {
    // 更多
    optionTool?.value?.onOpen({
      user:
        (props.bizType === '2' ? userInfo.value?.username : item.uploader) +
        ' | ' +
        item.updateTime,
      title: item.fileType === 3 ? item.spaceName : item.fileName,
      fileObj: item,
    });
  } else if (obj.index === 1) {
    // 分享
    console.log('分享');
    console.log(item);
    optionShare?.value?.onOpen({
      fileObj: item,
    });
  } else if (obj.index === 2) {
    // 删除
    console.log('删除');
    console.log(obj);
    optionDel?.value?.onOpen({ fileObj: item });
  }
};

const refreshPageList = () => {
  lkPageListRef.value?.refresh();
  // 重置isNoData状态，强制重新判断
  isNoData.value = false;

  // 通过更新key强制LkPageList组件重新渲染
  pageListKey.value += 1;
};

const delCb = async (item: any) => {
  console.log('删除1111', item);
  // 删除文件/文件夹
  if (item.fileType === 1 || item.fileType === 2) {
    const res = await (props.bizType === '1' ? mixBatchDelete : myMixBatchDelete)({
      list: [{ id: item.id, fileType: item.fileType }],
    });
    console.log(res);
    // 删除空间
  } else if (item.fileType === 3) {
    const res = await deleteSpaceFile({ id: item.id });
    console.log(res);
  } else {
    console.log('未知文件类型');
  }
  // 拉取空间数据并重新渲染
  refreshPageList();
};

const clickItem = (item: any) => {
  const maxPrivilege = item.privileges?.length ? Math.max(...item.privileges) : 4;
  userStore.curPrivilege = maxPrivilege || 4;
  if (item.fileType === 3 || item.fileType === 2) {
    const navTitle = props.bizType === '1' ? item.spaceName : item.folderName;
    // 跳转子级数据空间
    uni.navigateTo({
      url: `/pages-subpackages/data-space-pkg/sub-space/index?id=${item.id}&navTitle=${navTitle}&bizType=${props.bizType}&shareType=${item.shareType}`,
    });
  } else if (item.fileType === 1) {
    fileProps.value = {
      fileUrl: item.file?.fileUrl,
      fileType: item.fileType,
    };
  }
};

const formatFileSize = (bytes: number, withoutB?: boolean): string => {
  if (bytes === 0) return '0B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + (withoutB ? sizes[i][0] : sizes[i]);
};

function goToMySpace() {
  emit('goToMySpace');
}

// 引用AddSelect组件
const addSelectRef = ref<InstanceType<typeof AddSelect> | null>(null);

// 上传文件方法
const uploadFile = () => {
  try {
    if (addSelectRef.value) {
      // 使用弹出popup的方式上传文件
      addSelectRef.value.openLocalFileUpload();
    } else {
      console.error('AddSelect组件引用不存在');
      uni.showToast({
        title: '上传组件未加载完成，请稍后重试',
        icon: 'none',
      });
    }
  } catch (error) {
    console.error('调用上传方法出错:', error);
    uni.showToast({
      title: '上传初始化失败，请重试',
      icon: 'none',
    });
  }
};

// 仓库当前请求参数，跟踪变化
const currentExtraParams = ref({
  parentId: '0',
  privilege: 4,
});

// 组件key，用于强制重新渲染
const pageListKey = ref(0);

// 监听bizType变化，刷新页面数据
watch(
  () => props.bizType,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      console.log('bizType变化，从', oldVal, '变为', newVal);

      // 重置isNoData状态，强制重新判断
      isNoData.value = false;

      // 通过更新key强制LkPageList组件重新渲染
      pageListKey.value += 1;

      console.log('强制刷新PageList组件，新key:', pageListKey.value);
    }
  }
);

defineExpose({
  lkPageListRef,
  uploadFile,
});
</script>
<template>
  <view class="databaseItem">
    <NoData
      v-if="isNoData"
      :noDataType="userInfo?.roleType === '1' || userInfo?.roleType === '3' ? '2' : '1'"
      :spaceType="props.bizType"
      @goToMySpace="goToMySpace"
      @uploadFile="uploadFile"
    />
    <LkPageList
      ref="lkPageListRef"
      v-if="!isNoData && props.bizType === '1'"
      :key="`school-space-${pageListKey}`"
      :fetch-method="fetchSpaceFileList"
      :extra-params="{
        parentId: '0',
        privilege: 4,
      }"
      :process-data="processData"
      :page-size="10"
    >
      <template #default="{ list }">
        <view v-for="item in list" :key="item.id" class="contentItem">
          <view class="swipe-action" @click="clickItem(item)">
            <view class="swipe-action__content">
              <LkSvg
                class="fileIcon"
                width="84rpx"
                height="84rpx"
                :src="fileIcon(item)"
                :errorSrc="`/static/fileTypeIcon/unknown.svg`"
              />
              <view class="wrapTxt">
                <view class="fileName">{{
                  item.fileType === 3 ? item.spaceName : item.fileName
                }}</view>
                <view class="fileInfo">
                  <text class="fileSize" v-if="item.fileType === 1">{{
                    formatFileSize(item.fileSize)
                  }}</text>
                  <view class="sign" v-if="item.fileType === 1"></view>
                  <text class="fileDate">{{ item.updateTime }}</text>
                  <text class="fileCreator">{{ item.uploader }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
    </LkPageList>

    <LkPageList
      v-if="!isNoData && props.bizType === '2'"
      ref="lkPageListRef"
      :key="`my-space-${pageListKey}`"
      :fetch-method="fetchSpaceFileList"
      :extra-params="{
        parentId: '0',
        privilege: 4,
      }"
      :process-data="processData"
      :page-size="10"
    >
      <template #default="{ list }">
        <view v-for="item in list" :key="item.id" class="contentItem">
          <u-swipe-action>
            <u-swipe-action-item
              :options="getOptionsByItem(item)"
              @click="(obj: any) => clickOption(obj, item)"
            >
              <view class="swipe-action" @click="clickItem(item)">
                <view class="swipe-action__content">
                  <LkSvg
                    class="fileIcon"
                    width="84rpx"
                    height="84rpx"
                    :src="fileIcon(item)"
                    :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                  />
                  <view class="wrapTxt">
                    <view class="fileName">{{
                      item.fileType === 3 ? item.spaceName : item.fileName
                    }}</view>
                    <view class="fileInfo">
                      <text class="fileSize" v-if="item.fileType === 1">{{
                        formatFileSize(item.fileSize)
                      }}</text>
                      <view class="sign" v-if="item.fileType === 1"></view>
                      <text class="fileDate">{{ item.updateTime }}</text>
                      <text class="fileCreator">{{ item.uploader }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </u-swipe-action-item>
          </u-swipe-action>
        </view>
      </template>
    </LkPageList>
  </view>
  <FileViewer :file="fileProps" @resetFile="fileProps = { fileUrl: '', fileType: '' }" />
  <OptionTool ref="optionTool" @refreshPageList="refreshPageList" :bizType="props.bizType" />
  <OptionShare ref="optionShare" />
  <OptionDel ref="optionDel" @confirm="delCb" />
  <AddSelect
    ref="addSelectRef"
    :bizType="props.bizType"
    parentId="0"
    @refreshPageList="refreshPageList"
  />
</template>
<style lang="scss" scoped>
.databaseItem {
  padding: 0 16px;
  height: 100%;
  .contentItem {
    margin-top: 14px;
    ::v-deep .u-swipe-action-item__right {
      .u-swipe-action-item__right__button {
        &:last-child {
          border-radius: 0 14px 14px 0;
        }
      }
    }
    .swipe-action {
      border: none;
      .swipe-action__content {
        display: flex;
        align-items: center;
        padding-top: 14.39px;
        padding-bottom: 13.61px;
        border-radius: 10px;
        border: 1px solid #f4f4f4;
        background: #fff;
        padding-left: 16px;
        padding-right: 16px;
        .fileIcon {
          flex-shrink: 0;
          margin-right: 10px;
        }
        .wrapTxt {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .fileName {
            color: #1d2129;
            font-family: 'PingFang SC';
            font-size: 32rpx;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            word-break: break-all;
          }
          .fileInfo {
            display: flex;
            align-items: center;
            margin-top: 3px;
            .fileSize {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .sign {
              width: 1px;
              height: 9px;
              background: #86909c;
              margin: 0 6px;
            }
            .fileDate {
              font-family: 'PingFang SC';
              font-size: 24rpx;
              color: #86909c;
            }
            .fileCreator {
              margin-left: auto;
              font-size: 24rpx;
              color: #4e5969;
              letter-spacing: 0.06px;
            }
          }
        }
      }
    }
  }
}
</style>
