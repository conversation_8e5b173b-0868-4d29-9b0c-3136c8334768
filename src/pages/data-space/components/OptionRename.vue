<script lang="ts" setup>
import { defineOptions, ref, defineEmits } from 'vue';
import { renameFile, renameFolder } from '@/api/database';
import LkSvg from '@/components/svg/index.vue';
import LkDatabasePopup from '@/components/LkDatabase/LkDatabasePopup.vue';

const emit = defineEmits(['update:modelValue', 'refreshPageList']);

const lkDatabasePopupRef = ref<InstanceType<typeof LkDatabasePopup>>();
const fileName = ref('');

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

// 当前编辑的文件对象
const currentFile = ref<any>(null);

const onOpen = (value: any) => {
  console.log('onOpen', value);
  fileName.value = value?.fileObj?.fileName || '';
  currentFile.value = value?.fileObj;
  show.value = true;
};

const onClose = () => {
  show.value = false;
  fileName.value = '';
  currentFile.value = null;
};

const placeholderStyle = 'color: #86909C; font-size: 14px;';

const handleRename = () => {
  console.log(currentFile.value);
  if (!fileName.value || !currentFile.value) {
    uni.showToast({
      title: '文件名不能为空',
      icon: 'none',
    });
    return;
  }
  const api = currentFile.value.fileType === 1 ? renameFile : renameFolder;
  api({
    id: currentFile.value.id,
    [currentFile.value.fileType === 1 ? 'fileName' : 'folderName']: fileName.value,
  }).then(res => {
    console.log(res);
    uni.showToast({
      title: '文件重命名成功',
      icon: 'success',
    });
    onClose();
    emit('refreshPageList');
  });
};

defineExpose({ onOpen, onClose });
defineOptions({ name: 'OptionRename' });
</script>

<template>
  <up-popup :show="show" mode="bottom" :round="10" @close="onClose" v-bind="$attrs">
    <view class="addFolder">
      <view class="title">重命名</view>
      <view class="wrapClose">
        <LkSvg src="/static/database/close.svg" width="24px" height="24px" @tap="onClose" />
      </view>
      <view class="wrapContent">
        <view class="title">
          <view class="txt">文件名称</view>
          <LkSvg class="required" src="/static/database/required.svg" width="8px" height="8px" />
        </view>
        <view class="wrapInput">
          <up-input
            v-model="fileName"
            :show-action="false"
            placeholder="请输入"
            border="none"
            :placeholderStyle="placeholderStyle"
            clearable
          ></up-input>
        </view>
        <view class="wrapBtn">
          <LkButton :disabled="!fileName" type="primary" shape="round" @tap="handleRename"
            >确定</LkButton
          >
        </view>
      </view>
    </view>
  </up-popup>
  <LkDatabasePopup ref="lkDatabasePopupRef" bizType="2" :layoutType="2" />
</template>

<style lang="scss" scoped>
.add-select {
  background: #fff;
  border-radius: 14px 14px 0 0;
  min-height: 30vh;
  padding: 18px;
  padding-top: 16px;
  padding-bottom: 10px;
  .wrapClose {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  &__header {
    gap: 5px;
    flex-direction: column;
  }
  &__list {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    &-item {
      flex-direction: column;
      gap: 7px;
      padding: 10px;
      border-radius: 14px;
      background: white;
      flex: 1;
      .lk-text {
        color: #1d2129;
        font-size: 30rpx;
        white-space: nowrap;
      }
    }
  }
}
.addFolder {
  background: #fff;
  border-radius: 14px 14px 0 0;
  min-height: 30vh;
  padding: 18px;
  padding-top: 16px;
  padding-bottom: 14px;
  position: relative;
  > .title {
    font-size: 18px;
    font-weight: 600;
    color: #1d2129;
    text-align: center;
  }
  .wrapClose {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  .wrapContent {
    .title {
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #1d2129;
      margin-top: 14px;
      .txt {
      }
      .required {
        margin-left: 6px;
      }
    }
  }
  .wrapInput {
    display: flex;
    height: 48px;
    align-items: center;
    border-radius: 8px;
    background: #f2f3f5;
    margin-top: 10px;
    padding: 0 16px;
    .u-input {
      font-size: 14px;
    }
  }
  .wrapBtn {
    margin-top: 104px;
    .lk-button {
      width: 100%;
    }
  }
}
</style>
