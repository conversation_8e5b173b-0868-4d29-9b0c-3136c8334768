<script lang="ts" setup>
import { defineOptions, ref, reactive, onMounted, watch, onBeforeUnmount, computed } from 'vue';

import LkSvg from '@/components/svg/index.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';
import { uploadFileWithResume } from '@/utils/uploadWithResume';

const optionDelRef = ref<any>(null);

const actionOptions = reactive([
  {
    style: {
      backgroundColor: '#D54941',
      fontSize: '24rpx',
    },
    text: '删除',
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
  },
]);

const show = defineModel('show', {
  type: Boolean,
  default: false,
});

const allRecords = ref<any[]>([]);
const uploadingRecords = computed(() =>
  allRecords.value.filter(
    r => r.status === 'uploading' || r.status === 'failed' || r.status === 'paused'
  )
);
const finishedRecords = computed(() => allRecords.value.filter(r => r.status === 'success'));

watch(uploadingRecords, newVal => {
  console.log(newVal);
});

// 用于深度比较数组内容
function deepEqual(a: any, b: any) {
  return JSON.stringify(a) === JSON.stringify(b);
}

const refreshUploadRecords = () => {
  const records = uni.getStorageSync('uploadRecords') || [];
  if (!deepEqual(allRecords.value, records)) {
    allRecords.value = Array.isArray(records) ? records : [];
  }
};

const handleClick = (obj: any) => {
  optionDelRef.value.onOpen({ fileObj: obj });
};

const handleFileIcon = (item: any): string => {
  if (item.fileType === 3) {
    return `/static/fileTypeIcon/space.svg`;
  } else if (item.fileType === 2) {
    return `/static/fileTypeIcon/folder.svg`;
  } else if (item.fileType === 1) {
    // 截取文件扩展名
    const fileType = item.fileName.split('.').pop()?.toLowerCase();
    return `/static/fileTypeIcon/${fileType}.svg`;
  }
  return `/static/fileTypeIcon/unknown.svg`;
};

const onOpen = (value: any) => {
  show.value = true;
};

const onClose = () => {
  show.value = false;
};

// 重试断点续传
async function handleRetry(item: any) {
  console.log(item);
  // 可根据你的业务需要，传递 bizType、parentId 等参数
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === item.uniqueId);
  if (idx !== -1) {
    item.fileKey = records[idx].fileKey; // 赋值已有 fileKey，避免重试时 fileKey 为空
    item.fileName = records[idx].fileName; // 【新增】补全 fileName，保证 merge 分段接口有 fileName
  }
  item.status = 'uploading';
  await uploadFileWithResume(item, item.bizType, item.parentId, (progress: number) => {
    item.progress = progress;
    // 这里可触发本地存储和UI刷新
    let records = uni.getStorageSync('uploadRecords') || [];
    if (!Array.isArray(records)) records = [];
    const idx = records.findIndex((r: any) => r.uniqueId === item.uniqueId);
    if (idx !== -1) {
      records[idx].progress = progress;
      records[idx].status = progress === 100 ? 'success' : 'uploading';
      uni.setStorageSync('uploadRecords', records);
      uni.$emit('uploadRecordsUpdated');
    }
  });
}

// 暂停上传
function handlePause(item: any) {
  item.status = 'paused';
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === item.uniqueId);
  if (idx !== -1) {
    records[idx].status = 'paused';
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }
}

// 继续上传
function handleResume(item: any) {
  item.status = 'uploading';
  let records = uni.getStorageSync('uploadRecords') || [];
  if (!Array.isArray(records)) records = [];
  const idx = records.findIndex((r: any) => r.uniqueId === item.uniqueId);
  if (idx !== -1) {
    records[idx].status = 'uploading';
    uni.setStorageSync('uploadRecords', records);
    uni.$emit('uploadRecordsUpdated');
  }
  handleRetry(item);
}

defineExpose({ onOpen, onClose });
defineOptions({ name: 'RecordTransfer' });

onMounted(() => {
  refreshUploadRecords();
  uni.$on('uploadRecordsUpdated', refreshUploadRecords);
});

onBeforeUnmount(() => {
  uni.$off('uploadRecordsUpdated', refreshUploadRecords);
});

// 重新获取并重新渲染数据
const refreshRecordTransfer = () => {
  console.log('refreshRecordTransfer');
  // 从本地存储获取最新的上传记录
  const records = uni.getStorageSync('uploadRecords') || [];
  allRecords.value = Array.isArray(records) ? records : [];
};
</script>

<template>
  <up-popup closeable :show="show" :round="10" @close="onClose" v-bind="$attrs">
    <view class="record">
      <view class="record__header flex-center">
        <LkText bold size="large">传输记录</LkText>
      </view>
      <scroll-view scroll-y class="record__content">
        <up-swipe-action>
          <view class="record__content-uploading" v-if="uploadingRecords.length">
            <view class="record__content-header">
              <LkText bold>上传中({{ uploadingRecords.length }})</LkText>
            </view>
            <template v-for="item in uploadingRecords" :key="item.uniqueId">
              <up-swipe-action-item
                :options="actionOptions"
                @click="handleClick(item)"
                class="record__card"
              >
                <view class="record__body-card" @touchmove.stop>
                  <view class="record__item">
                    <view class="record__item-icon">
                      <LkSvg
                        class="fileIcon"
                        width="80rpx"
                        height="80rpx"
                        :src="handleFileIcon(item)"
                        :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                      />
                    </view>
                    <view class="record__item-info">
                      <LkText type="primary" class="up-line-1">{{ item.fileName }}</LkText>
                      <view class="record__item-desc">
                        <LkText type="neutral" size="xs"
                          >{{ (item.fileSize / 1024).toFixed(1) }}KB</LkText
                        >
                        <up-line length="8" color="#666" direction="col" margin="0 10rpx" />
                        <LkText type="neutral" size="xs">{{
                          new Date(item.uploadTime).toLocaleDateString()
                        }}</LkText>
                      </view>
                    </view>
                  </view>
                  <up-line-progress
                    height="8rpx"
                    :percentage="item.progress"
                    :showText="false"
                    style="margin-top: 20rpx; margin-bottom: 20rpx"
                  />
                  <view class="record__body-card-footer">
                    <view class="record__body-card-footer-left">
                      <up-icon name="car" size="30rpx" />
                      <LkText type="neutral" size="xs">上传空间</LkText>
                    </view>
                    <view class="record__body-card-footer-right">
                      <template v-if="item.status === 'uploading'">
                        <LkText type="neutral" size="xs">{{ item.progress }}%</LkText>
                        <span style="padding-inline: 10rpx">·</span>
                        <LkText type="neutral" size="xs">上传中...</LkText>
                        <!-- <up-button size="mini" type="warning" @click.stop="handlePause(item)"
                          >暂停</up-button
                        > -->
                      </template>
                      <!-- <template v-else-if="item.status === 'paused'">
                        <LkText type="neutral" size="xs" customStyle="color: #FA9550;"
                          >已暂停</LkText
                        >
                        <up-button size="mini" type="primary" @click.stop="handleResume(item)"
                          >继续</up-button
                        >
                      </template> -->
                      <template v-else-if="item.status === 'failed'">
                        <LkText type="neutral" size="xs" customStyle="color: #F6685D;"
                          >上传失败</LkText
                        >
                        <up-button size="mini" type="primary" @click.stop="handleRetry(item)"
                          >重试</up-button
                        >
                      </template>
                    </view>
                  </view>
                </view>
              </up-swipe-action-item>
            </template>
          </view>
          <view class="record__content-uploaded" v-if="finishedRecords.length">
            <view class="record__content-header">
              <LkText bold>已完成({{ finishedRecords.length }})</LkText>
            </view>
            <template v-for="item in finishedRecords" :key="item.uniqueId">
              <up-swipe-action-item
                :options="actionOptions"
                @click="handleClick(item)"
                class="record__card"
              >
                <view class="record__body-card" @touchmove.stop>
                  <view class="record__item">
                    <view class="record__item-icon">
                      <LkSvg
                        class="fileIcon"
                        width="80rpx"
                        height="80rpx"
                        :src="handleFileIcon(item)"
                        :errorSrc="`/static/fileTypeIcon/unknown.svg`"
                      />
                    </view>
                    <view class="record__item-info">
                      <LkText type="primary" class="up-line-1">{{ item.fileName }}</LkText>
                      <view class="record__item-desc">
                        <LkText type="neutral" size="xs"
                          >{{ (item.fileSize / 1024).toFixed(1) }}KB</LkText
                        >
                        <up-line length="8" color="#666" direction="col" margin="0 10rpx" />
                        <LkText type="neutral" size="xs">{{
                          new Date(item.uploadTime).toLocaleDateString()
                        }}</LkText>
                      </view>
                    </view>
                  </view>
                  <up-line-progress
                    height="8rpx"
                    :percentage="100"
                    :showText="false"
                    style="margin-top: 20rpx; margin-bottom: 20rpx"
                  />
                  <view class="record__body-card-footer">
                    <view class="record__body-card-footer-left">
                      <up-icon name="car" size="30rpx" />
                      <LkText type="neutral" size="xs">上传空间</LkText>
                    </view>
                    <view class="record__body-card-footer-right">
                      <LkText type="neutral" size="xs" customStyle="color: #2BA471;"
                        >上传成功</LkText
                      >
                    </view>
                  </view>
                </view>
              </up-swipe-action-item>
            </template>
          </view>
          <view v-if="!uploadingRecords.length && !finishedRecords.length">
            <LkText type="neutral" size="xs">暂无上传记录</LkText>
          </view>
        </up-swipe-action>
      </scroll-view>
    </view>
    <OptionDel ref="optionDelRef" @confirm="refreshRecordTransfer" />
  </up-popup>
</template>

<style lang="scss" scoped>
.record {
  background: #fff;
  border-radius: 28rpx;
  padding: 20rpx;
  padding-top: 0;
  ::v-deep .u-swipe-action-item__right {
    border-radius: 0 20rpx 20rpx 0;
    overflow: hidden;
    padding: 2rpx;
  }
  &__header {
    height: 50px;
    justify-content: flex-start;
  }
  &__content {
    max-height: 85vh;
    min-height: 40vh;
    &-header {
    }
    &-uploaded {
      margin-top: 30rpx;
    }
  }
  &__card {
    margin-top: 30rpx;
    border: 1rpx solid #f4f4f4;
    border-radius: 20rpx;
    box-shadow: 0px 0px 15.2px 0px rgba(237, 237, 237, 0.62);
  }
  &__item {
    display: flex;
    align-items: stretch;
    gap: 20rpx;
    &-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8rpx;
      flex: 1;
    }
    &-desc {
      display: flex;
      align-items: center;
    }
    &-tools {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
  &__body-card {
    padding: 25rpx 30rpx;
    ::v-deep .u-line-progress__line {
      background: linear-gradient(
        90deg,
        #cfd7ff 0%,
        #788df3 43.75%,
        #ed78cb 87.02%,
        #fcb97e 98.56%
      );
    }
    &-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-left {
        display: flex;
        align-items: center;
      }
      &-right {
      }
    }
  }
}

// 禁用已完成进度条动画，防止重绘闪烁
::v-deep .record__content-uploaded .u-line-progress__line {
  transition: none !important;
}
</style>
