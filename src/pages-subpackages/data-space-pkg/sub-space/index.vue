<script setup lang="ts">
import { ref, reactive, nextTick, watch, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { mixBatchDelete, myMixBatchDelete, deleteSpaceFile } from '@/api/database';
import AddSelect from '@/pages/data-space/components/AddSelect.vue';

import LkDatabaseItem from '@/components/LkDatabase/LkDatabaseItem.vue';
import OptionTool from '@/pages/data-space/components/OptionTool.vue';
import OptionShare from '@/pages/data-space/components/OptionShare.vue';
import OptionDel from '@/pages/data-space/components/OptionDel.vue';
import LkStepGuide from '@/components/LkStepGuide/index.vue';
import LkSvg from '@/components/svg/index.vue';
import { useUserStore } from '@/store/userStore';

type NavOptions = {
  id: string;
  navTitle: string;
  bizType: string;
};

const urlOptions = ref<NavOptions>();
const addSelectRef = ref<any>(null);
const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);

const fabClick = () => {
  addSelectRef.value.onOpen({
    title: urlOptions.value?.navTitle,
    type: urlOptions.value?.bizType === '1' ? 'subAdd' : 'add',
  });
};
const stepGuideRef = ref();
const needGuide = ref(false);
const guideSteps = [
  {
    target: '.addSpace',
    title: '',
    content: '在学校空间和我的空间中，点击「添加」工作成果文件。',
    position: 'topRight',
    lollipop: true,
    offsetX: 35,
    offsetY: -20,
  },
  {
    target: '.more-checked',
    title: '',
    content: '点击「多选」文件支持批量操作。',
    position: 'bottom',
    lollipop: true,
    offsetX: 18,
    offsetY: -30,
  },
  {
    target: '.u-swipe-action-item:nth-child(3)',
    title: '',
    content: '左滑可对文件进行分享等操作哦~',
    position: 'bottom',
  },
] as any[];

const onGuideComplete = () => {
  const data = uni.getStorageSync('Guide') || {};
  uni.setStorageSync('Guide', { ...data, DataSpace: true });

  nextTick(() => uni.navigateBack());
};

onLoad((options: any) => {
  console.log(options);
  urlOptions.value = { ...options } as NavOptions;
  if (options.guide === 'true' && !uni.getStorageSync('Guide')?.DataSpace) {
    needGuide.value = true;
  }
});

const navBack = () => {
  uni.navigateBack();
};

const optionsList = ref<any[]>([
  {
    style: {
      backgroundColor: '#A6A6A6',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/5849fcbac6a1721968c83830dfb3f622.svg',
  },
  {
    style: {
      backgroundColor: '#8B8B8B',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/8122eec722aa08581f49a2b32a27317a.svg',
  },
  {
    style: {
      backgroundColor: '#D54941',
    },
    icon: 'https://huayun-ai-obs-public.obs.cn-south-1.myhuaweicloud.com/bf5e481fc72554fa10d8a5c6390553ab.svg',
  },
]);

const optionTool = ref<InstanceType<typeof OptionTool>>();
const optionShare = ref<InstanceType<typeof OptionShare>>();
const optionDel = ref<InstanceType<typeof OptionDel>>();
const lkDatabaseItemRef = ref<InstanceType<typeof LkDatabaseItem>>();

const clickOption = async (payload: { eventData: any; itemData: any }) => {
  const { eventData: optionsData, itemData: currentItem } = payload;
  if (optionsData.index === 0) {
    // 更多
    optionTool?.value?.onOpen({
      user:
        (urlOptions.value?.bizType === '2' ? userInfo.value?.username : currentItem.uploader) +
        ' | ' +
        currentItem.updateTime,
      title: currentItem.fileType === 3 ? currentItem.spaceName : currentItem.fileName,
      fileObj: currentItem,
    });
  } else if (optionsData.index === 1) {
    // 分享
    console.log('分享');
    optionShare?.value?.onOpen({ fileObj: currentItem });
  } else if (optionsData.index === 2) {
    // 删除
    console.log('删除');
    optionDel?.value?.onOpen({
      fileObj: currentItem,
    });
  }
};

const delCb = async (item: any) => {
  console.log('删除1111', item);
  // 删除文件/文件夹
  if (item.fileType === 1 || item.fileType === 2) {
    const res = await (urlOptions.value?.bizType === '1' ? mixBatchDelete : myMixBatchDelete)({
      list: [{ id: item.id, fileType: item.fileType }],
    });
    console.log(res);
    // 删除空间
  } else if (item.fileType === 3) {
    const res = await deleteSpaceFile({ id: item.id });
    console.log(res);
  } else {
    console.log('未知文件类型');
  }
  // 拉取空间数据并重新渲染
  refreshPageList();
};

const refreshPageList = () => {
  console.log('refreshPageList');
  lkDatabaseItemRef.value?.refreshPageList();
};

// 添加数据加载完成事件处理
const handleDataLoaded = (data: any[]) => {
  console.log('数据加载完成:', data);
  if (!needGuide.value) return;
  const index = data.length > 0 ? (data.length <= 3 ? data.length : 3) : 0;
  nextTick(() => {
    setTimeout(() => {
      startGuideWhenListReady(index);
    }, 200);
  });
};

const startGuideWhenListReady = (index: number) => {
  if (index <= 0) {
    onGuideComplete();
    return;
  }
  const checkQuery = uni.createSelectorQuery();
  const target = `.u-swipe-action-item:nth-child(${index})`;

  checkQuery
    .select(target)
    .boundingClientRect((rect: any) => {
      if (rect) {
        guideSteps[2].target = target;
        // 等待滑动单元格展开后启动引导
        stepGuideRef.value?.start();
      }
    })
    .exec();
};

defineOptions({ name: 'dataSubSpace' });
</script>
<template>
  <view class="sub-space">
    <up-navbar :title="urlOptions?.navTitle">
      <template #left>
        <up-icon name="arrow-left" size="40rpx" color="#000" @tap="navBack" />
        <up-icon name="server-fill" size="40rpx" color="#000" style="margin-left: 20rpx" />
      </template>
      <template #right>
        <up-icon
          name="share"
          size="40rpx"
          color="#000"
          style="margin-right: 20rpx"
          class="more-checked"
        />
        <up-icon name="search" size="44rpx" color="#000" />
      </template>
    </up-navbar>
    <view class="sub-space__body">
      <!-- 全局数据空间组件 -->
      <LkDatabaseItem
        ref="lkDatabaseItemRef"
        :layoutType="1"
        :parentId="urlOptions?.id || ''"
        :bizType="urlOptions?.bizType || ''"
        :optionsList="optionsList"
        :needGuide="needGuide"
        @clickOption="clickOption"
        @dataLoaded="handleDataLoaded"
      />
      <LkStepGuide ref="stepGuideRef" :steps="guideSteps" @complete="onGuideComplete" />
    </view>
    <!-- +号 -->
    <view class="addSpace" @tap="fabClick">
      <LkSvg width="24px" height="24px" src="/static/database/addSpace.svg" />
    </view>
    <!-- <uni-fab horizontal="right" vertical="bottom" @fabClick="fabClick" class="sub-space__fab" /> -->
    <AddSelect
      ref="addSelectRef"
      :bizType="urlOptions?.bizType || ''"
      :parentId="urlOptions?.id || ''"
      @refreshPageList="refreshPageList"
    />
    <OptionTool
      ref="optionTool"
      @refreshPageList="refreshPageList"
      :bizType="urlOptions?.bizType || ''"
    />
    <OptionShare ref="optionShare" />
    <OptionDel ref="optionDel" @confirm="delCb" />
  </view>
</template>
<style lang="scss" scoped>
.sub-space {
  background-color: white;
  &__body {
    padding: 10rpx 20rpx;
    display: flex;
    flex-direction: column;
    padding-top: calc(var(--status-bar-height) + 80rpx);
  }
}
.addSpace {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(180deg, #4da3ff 0%, #7d4dff 100%);
  box-shadow:
    0px 3px 14px 2px rgba(0, 0, 0, 0.05),
    0px 8px 10px 1px rgba(0, 0, 0, 0.06),
    0px 5px 5px -3px rgba(0, 0, 0, 0.1);
  position: fixed;
  bottom: 10vh;
  right: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
