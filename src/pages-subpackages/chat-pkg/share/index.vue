<template>
  <view class="share-page-container">
    <!-- 1. Navigation Bar -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" @click="goBack"></uni-icons>
    </view>

    <scroll-view scroll-y class="scroll-content">
      <view class="content-wrapper">
        <!-- 2. Header Section -->
        <view class="header-section">
          <view class="logo-image-container">
            <!-- 请替换为您的logo图片路径 -->
            <image class="logo-icon" :src="appDetail?.avatarUrl" mode="aspectFit"></image>
          </view>
          <text class="main-title">{{ appDetail?.name }}</text>
          <text class="sub-title">{{ appDetail?.intro || '暂无介绍' }}</text>
        </view>

        <view class="action-buttons">
          <button
            class="btn favorites-btn"
            :class="{ favorited: addFavorites }"
            @click="handleFavorites"
          >
            <LkSvg
              v-if="!addFavorites"
              width="40rpx"
              height="40rpx"
              src="/static/chat/add-circle.svg"
              color="#fff"
            ></LkSvg>
            <view v-if="addFavorites" class="favorites-icon">
              <LkSvg
                width="40rpx"
                height="40rpx"
                src="/static/chat/check_fill.svg"
                color="#fff"
              ></LkSvg>
            </view>
            我的常用
          </button>
          <button class="btn share-btn" @click="handleShareApp">
            <LkSvg
              width="40rpx"
              height="40rpx"
              src="/static/chat/share-pg.svg"
              color="#333333"
            ></LkSvg>
            分享应用
          </button>
        </view>
        <view class="divider-line"> </view>

        <view class="history-section">
          <text class="history-title">历史会话</text>
          <view class="search-bar">
            <uni-icons type="search" color="#999999" size="18"></uni-icons>
            <input
              class="search-input"
              placeholder="请输入关键词搜索"
              placeholder-style="color:#999999"
              v-model="searchKeyword"
              @input="onSearchInput"
            />
          </view>

          <!-- 历史会话内容列表 -->
          <scroll-view class="chat-content" scroll-y @scrolltolower="loadMore">
            <view v-if="filteredChatData.length > 0">
              <view v-for="(day, index) in filteredChatData" :key="index" class="chat-day">
                <view class="chat-day-title">{{ day.timeLabel }}</view>
                <view
                  v-for="(message, msgIndex) in day.messages"
                  :key="msgIndex"
                  class="chat-message"
                  @click="chatDetail(message)"
                >
                  {{ message.title }}
                </view>
              </view>
            </view>
            <view v-else class="empty-state">
              <LkSvg
                width="280rpx"
                height="230rpx"
                src="/static/chat/empty_box.svg"
                color="#999999"
              ></LkSvg>
              <text class="empty-text">暂无使用数据</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </scroll-view>
    <!-- 分享弹窗 -->
    <up-overlay
      :show="showSharePopup"
      @close="showSharePopup = false"
      :custom-style="{
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }"
    >
      <view class="share-dialog">
        <view class="share-dialog-centered" @tap.stop>
          <view class="logo-image-container">
            <image class="logo-icon" :src="appDetail?.avatarUrl" mode="aspectFit"></image>
          </view>
          <text class="dalog-main-title">{{ appDetail?.name }}</text>
          <text class="dalog-sub-title">{{ formattedIntro }}</text>
          <view class="share-dialog-divider">
            <view class="share-dialog-qrcode">
              <!-- 使用条件渲染确保二维码正确显示 -->
              <view v-if="qrCodeValue && showSharePopup" class="qrcode-container">
                <up-qrcode
                  ref="qrCodeRef"
                  :size="shareDialogDimensions.qrCodeSize * 0.85"
                  :val="qrCodeValue"
                  showLoading
                  loadingText="loading..."
                  :color="'#000000'"
                  :bgColor="'#FFFFFF'"
                  :margin="0"
                  @result="onQRCodeResult"
                  @complete="onQRCodeComplete"
                  @error="onQRCodeError"
                ></up-qrcode>
              </view>
              <!-- 二维码加载失败时的备用方案 -->
              <view v-else-if="qrCodeError" class="qrcode-error">
                <text class="error-text">二维码加载失败</text>
                <button class="retry-btn" @click="retryQrCode">重试</button>
              </view>
              <!-- 加载中状态 -->
              <view v-else class="qrcode-loading">
                <text>二维码生成中...</text>
              </view>
            </view>
            <view class="share-dialog-qrcode-text">
              <text>扫描二维码，体验应用</text>
            </view>
            <image
              class="share-dialog-qrcode-logo"
              src="https://huayun-ai-obs-public.huayuntiantu.com/ffd5b4ea-f9bf-43a9-b2bd-6bb852639ba6.png"
              mode="scaleToFill"
            />
          </view>
        </view>
        <view class="share-actions-bottom-panel">
          <view class="share-options-grid">
            <view class="option-item" @click="shareTo('moments')">
              <view class="icon-wrapper">
                <LkSvg
                  :width="shareDialogDimensions.iconSize + 'px'"
                  :height="shareDialogDimensions.iconSize + 'px'"
                  src="/static/chat/pyq_logo.svg"
                ></LkSvg>
              </view>
              <text class="option-text">朋友圈</text>
            </view>
            <view class="option-item" @click="shareTo('wechat')">
              <view class="icon-wrapper">
                <LkSvg
                  :width="shareDialogDimensions.iconSize + 'px'"
                  :height="shareDialogDimensions.iconSize + 'px'"
                  src="/static/chat/wx_logo.svg"
                ></LkSvg>
              </view>
              <text class="option-text">微信好友</text>
            </view>
            <view class="option-item" @click="saveToAlbum">
              <view class="icon-wrapper">
                <LkSvg
                  :width="shareDialogDimensions.iconSize + 'px'"
                  :height="shareDialogDimensions.iconSize + 'px'"
                  src="/static/chat/save_photo.svg"
                ></LkSvg>
              </view>
              <text class="option-text">保存相册</text>
            </view>
          </view>
          <view class="actions-panel-divider"></view>
          <view class="cancel-row" @click="closeSharePopup">
            <text class="cancel-text-label">取消</text>
          </view>
        </view>
      </view>
    </up-overlay>

    <!-- Canvas for drawing share image (hidden) -->
    <canvas
      canvas-id="shareCanvas"
      id="shareCanvas"
      :style="{
        width: canvasWidthPxComputed + 'px',
        height: canvasHeightPxComputed + 'px',
        position: 'fixed', // Use fixed to ensure it's off-screen
        top: '-9999px',
        left: '-9999px',
        zIndex: -10, // ensure it's not interactive
      }"
    ></canvas>

    <!-- Toast 提示组件 -->
    <LkToast ref="uToastRef"></LkToast>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, computed } from 'vue';
import LkSvg from '../../../components/svg/index.vue';
import * as chatApi from '../../../api/chat';
import * as userCommonApi from '../../../api/userCommon';
import { getBaseUrl } from '@/common/ai/url';

// 导入 uni-share 插件
import UniShare from '../../../uni_modules/uni-share/js_sdk/uni-share.js';
const uToastRef = ref();

import {
  generateAndSaveShareImage,
  type GenerateShareImageOptions,
} from '../../../utils/canvasShare';

interface ChatMessage {
  chatId: string;
  title: string;
  tenantAppId: string;
  appAvatarUrl: string;
  updateTime: string;
  timeLabel?: string;
  timeYmd?: string;
  timeHm?: string;
}

interface ChatGroup {
  timeLabel: string;
  messages: ChatMessage[];
}

interface AppDetail {
  id: string;
  appId: string;
  name: string;
  intro: string;
  avatarUrl: string;
  isCommonApp?: number;
}

// For canvas drawing
const { proxy } = getCurrentInstance()!;
const systemInfo = uni.getSystemInfoSync();
const dpr = systemInfo.pixelRatio;

// Define canvas dimensions in rpx (these match the visual design)
const canvasWidthRpx = 690;
const canvasHeightRpx = 824 + 60; // 824 for dialog, 60 for logo overflow above

// Computed properties to convert rpx to px for canvas style and drawing
const canvasWidthPxComputed = computed(() => uni.upx2px(canvasWidthRpx));
const canvasHeightPxComputed = computed(() => uni.upx2px(canvasHeightRpx));

// 分享弹窗尺寸计算 - 使用px单位避免平板设备样式过大
const shareDialogDimensions = computed(() => {
  const screenWidth = systemInfo.screenWidth;
  const isTablet = screenWidth >= 768; // 判断是否为平板设备

  // 基础尺寸（以iPhone为基准）
  const baseWidth = 311; // 622rpx / 2
  const baseHeight = 412; // 824rpx / 2

  // 根据设备类型调整尺寸
  let dialogWidth, dialogHeight;

  if (isTablet) {
    // 平板设备：使用固定的合理尺寸
    dialogWidth = Math.min(400, screenWidth * 0.6);
    dialogHeight = dialogWidth * (baseHeight / baseWidth);
  } else {
    // 手机设备：使用屏幕宽度的比例
    dialogWidth = Math.min(baseWidth, screenWidth * 0.85);
    dialogHeight = dialogWidth * (baseHeight / baseWidth);
  }

  return {
    width: dialogWidth,
    height: dialogHeight,
    logoSize: isTablet ? 60 : 66, // logo容器大小
    logoIconSize: isTablet ? 32 : 35, // logo图标大小
    titleFontSize: isTablet ? 16 : 18, // 标题字体大小
    subtitleFontSize: isTablet ? 11 : 12, // 副标题字体大小
    qrCodeSize: isTablet ? 120 : 140, // 二维码大小
    qrCodeTextSize: isTablet ? 11 : 12, // 二维码下方文字大小
    bottomLogoWidth: isTablet ? 50 : 57, // 底部logo宽度
    bottomLogoHeight: isTablet ? 20 : 23, // 底部logo高度
    iconSize: isTablet ? 35 : 40, // 分享选项图标大小
    optionTextSize: isTablet ? 11 : 12, // 分享选项文字大小
    isTablet,
  };
});

// 获取设备信息
const getDeviceInfo = () => {
  const deviceInfo = uni.getDeviceInfo();
  return deviceInfo;
};

// 日期处理工具 - Restored as it is used by onPageDataLoad
const dateUtil = {
  // 解析日期字符串为Date对象
  parseDate(dateStr: string): Date {
    return new Date(dateStr);
  },

  // 获取日期的开始时间（00:00:00）
  startOfDay(date: Date): Date {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  },

  // 计算两个日期之间的天数差
  diffDays(date1: Date, date2: Date): number {
    const diffTime = Math.abs(date1.getTime() - date2.getTime());
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  },

  // 获取日期的年份
  getYear(date: Date): number {
    return date.getFullYear();
  },

  // 获取日期的月份（0-11）
  getMonth(date: Date): number {
    return date.getMonth();
  },

  // 获取日期是星期几（0-6，0表示星期日）
  getDay(date: Date): number {
    return date.getDay();
  },

  // 格式化日期为 YYYY-MM-DD
  formatYMD(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 格式化时间为 HH:MM
  formatHM(date: Date): string {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  },
};

const showSharePopup = ref(false);
const addFavorites = ref(false);
const chatData = ref<ChatMessage[]>([]);
const filteredChatData = ref<ChatGroup[]>([]);
const current = ref(1);
const size = ref(20); // 每页加载20条数据
const total = ref(0); // 总数据条数
const hasMore = ref(true); // 是否有更多数据
const searchKeyword = ref(''); // 搜索关键字
const appId = ref<string | null>(null);
const appDetail = ref<AppDetail | null>(null);
const logoUrl = ref(
  'https://huayun-ai-obs-public.huayuntiantu.com/ffd5b4ea-f9bf-43a9-b2bd-6bb852639ba6.png'
);

// uni-share 实例
const uniShare = new UniShare();

// 分享图片相关
const shareImageUrl = ref(''); // 生成的分享图片URL

// 二维码相关
interface QrCodeInstance {
  // up-qrcode 组件主要通过 @result 和 @complete 回调提供数据
  $el?: any;
  [key: string]: any; // 允许其他属性
}

const qrCodeRef = ref<QrCodeInstance | null>(null);
const qrCodeValue = ref(''); // 默认二维码链接
const qrCodeImageUrl = ref(''); // 二维码图片URL
const sharingToken = ref(''); // 分享token
const qrCodeError = ref(false); // 二维码错误状态

// 二维码生成完成回调
const onQRCodeComplete = (dataURL: string) => {
  if (dataURL && dataURL.length > 50) {
    qrCodeImageUrl.value = dataURL;
    qrCodeError.value = false;
  }
};

// 二维码生成结果回调（主要回调）
const onQRCodeResult = (res: any) => {
  console.log('[QR Result] 二维码生成结果回调触发:', res);

  // 尝试从不同的属性中获取二维码数据
  let dataURL = '';
  if (res && typeof res === 'string') {
    dataURL = res;
  } else if (res && res.dataURL) {
    dataURL = res.dataURL;
  } else if (res && res.data) {
    dataURL = res.data;
  } else if (res && res.tempFilePath) {
    dataURL = res.tempFilePath;
  }

  if (dataURL && dataURL.length > 50) {
    qrCodeImageUrl.value = dataURL;
    qrCodeError.value = false;
  }
};

// 二维码错误回调
const onQRCodeError = (error: any) => {
  console.error('[QR Error] 二维码生成失败:', error);
  qrCodeError.value = true;
};

// 重试生成二维码
const retryQrCode = async () => {
  qrCodeError.value = false;
  if (appDetail.value) {
    qrCodeValue.value = await generateAppShareLink(appDetail.value);
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 加载页面数据
onMounted(() => {
  console.log('[Mount] 页面挂载开始');

  // 检查uview-plus组件是否正确加载
  console.log('[Mount] uni.$u:', uni.$u);

  // 检查设备信息
  const deviceInfo = getDeviceInfo();
  console.log('[Mount] 设备信息:', deviceInfo);

  const tenantAppId = uni.getStorageSync('selectApp');
  if (tenantAppId && tenantAppId.id) {
    appId.value = tenantAppId.id;
    getAppDetail();
  }
  loadChatData();

  console.log('[Mount] 页面挂载完成');
});

// 添加页面返回监听，用于隐藏分享菜单
const onBackPress = ({ from }: { from: string }) => {
  console.log('onBackPress from:', from);
  if (from === 'backbutton' && uniShare.isShow) {
    // 如果分享菜单显示中，先隐藏分享菜单
    uniShare.hide();
    return true; // 阻止默认返回行为
  }
  return false; // 允许正常返回
};

// 获取分享token
const getSharingTokenApi = async () => {
  try {
    const response = await chatApi.getSharingToken();
    console.log('response', response);
    sharingToken.value = response.accessToken;
    return sharingToken.value;
  } catch (error) {
    console.error('获取分享token失败:', error);
    return '';
  }
};

// 生成应用分享链接
const generateAppShareLink = async (app: AppDetail): Promise<string> => {
  try {
    // 获取token
    const token = await getSharingTokenApi();
    // 获取设备ID
    // 构建新的URL格式
    const baseUrl = `${getBaseUrl()}/temp_chat`;
    const params = new URLSearchParams();

    params.append('appId', app.id || '');
    params.append('token', token || '');
    console.log('url', `${baseUrl}?${params.toString()}`);
    return `${baseUrl}?${params.toString()}`;
  } catch (error) {
    const token = await getSharingTokenApi();
    console.error(
      '生成分享链接失败:',
      `${getBaseUrl()}/temp_chat?appId=${app.id || ''}&token=${token}`
    );
    // 如果出错，返回一个基础链接
    return `${getBaseUrl()}/temp_chat?appId=${app.id || ''}&token=${token}`;
  }
};

// 获取应用详情
const getAppDetail = async () => {
  if (!appId.value) return;

  try {
    const res: AppDetail = await chatApi.getAppDetailByAppId({
      id: appId.value,
    });

    appDetail.value = res;
    console.log('res', res);
    addFavorites.value = res.isCommonApp === 1;

    // 设置二维码值为应用分享链接
    qrCodeValue.value = await generateAppShareLink(res);
    console.log('[QR] 二维码值已设置:', qrCodeValue.value);

    // 给二维码组件一些时间来渲染，然后尝试获取图片
    setTimeout(async () => {
      console.log('[QR] 延迟后尝试获取二维码图片...');
      if (qrCodeRef.value && typeof qrCodeRef.value.getDataURL === 'function') {
        try {
          const dataURL = await qrCodeRef.value.getDataURL();
          if (dataURL && dataURL.length > 50) {
            qrCodeImageUrl.value = dataURL;
            console.log('[QR] 延迟获取二维码成功');
          }
        } catch (err) {
          console.warn('[QR] 延迟获取二维码失败:', err);
        }
      }
    }, 1000); // 给组件1秒时间渲染
  } catch (error) {
    console.error('获取应用详情失败:', error);
  }
};

// 加载聊天数据
const loadChatData = async () => {
  if (!hasMore.value) return; // 如果没有更多数据，则不再加载

  try {
    const response = appId.value
      ? await chatApi.getChatList({
          appId: appId.value,
          keyword: null,
          current: current.value,
          size: size.value,
        })
      : await chatApi.getChatKeyWordList({
          appId: null,
          keyword: null,
          current: current.value,
          size: size.value,
        });

    renderData(response.records, response.total);
  } catch (error) {
    console.error('加载聊天数据失败:', error);
  }
};

// 根据关键字加载聊天数据
const loadChatKeyWordData = async () => {
  if (!hasMore.value) return;

  try {
    const response = appId.value
      ? await chatApi.getChatList({
          appId: appId.value,
          keyword: searchKeyword.value,
          current: current.value,
          size: size.value,
        })
      : await chatApi.getChatKeyWordList({
          appId: null,
          keyword: searchKeyword.value,
          current: current.value,
          size: size.value,
        });

    renderData(response.records, response.total);
  } catch (error) {
    console.error('加载搜索数据失败:', error);
  }
};

// 渲染数据
const renderData = (data: ChatMessage[], totalCount: number) => {
  const processedData = onPageDataLoad(data);
  total.value = totalCount;

  // 如果当前加载的数据条数大于等于总数据条数，说明没有更多数据了
  if (chatData.value.length + processedData.length >= total.value) {
    hasMore.value = false;
  }

  chatData.value = chatData.value.concat(processedData);
  filteredChatData.value = groupByTimeLabel(chatData.value);
};

// 搜索输入处理
const onSearchInput = function (e: any) {
  console.log('onSearchInput', e.detail.value);
  searchKeyword.value = e.detail.value;
  current.value = 1;
  chatData.value = [];
  hasMore.value = true; // 重置加载更多的标志

  if (searchKeyword.value) {
    loadChatKeyWordData();
  } else {
    loadChatData();
  }
};

// 加载更多数据
const loadMore = () => {
  if (hasMore.value) {
    current.value++;

    if (searchKeyword.value) {
      loadChatKeyWordData();
    } else {
      loadChatData();
    }
  }
};

// 处理页面数据时间格式化
const onPageDataLoad = (data: ChatMessage[]) => {
  const today = dateUtil.startOfDay(new Date());

  return data.map(it => {
    const updateDate = dateUtil.parseDate(it.updateTime);
    const updateDay = dateUtil.startOfDay(updateDate);
    const dayDiff = dateUtil.diffDays(today, updateDay);
    let label = '';

    if (dayDiff === 0) {
      label = '今天';
    } else if (dayDiff === 1) {
      label = '昨天';
    } else if (dayDiff <= 7) {
      // 获取今天和更新日期的星期几（1-7，其中7表示星期日）
      const todayDay = dateUtil.getDay(today) || 7;
      const updateDayOfWeek = dateUtil.getDay(updateDay) || 7;

      // 如果更新日期的星期小于今天的星期，说明在本周内
      if (updateDayOfWeek < todayDay) {
        label = '本周';
      } else {
        label = `${dateUtil.getMonth(updateDay) + 1}月`;
      }
    } else if (dateUtil.getYear(updateDay) === dateUtil.getYear(today)) {
      label = `${dateUtil.getMonth(updateDay) + 1}月`;
    } else {
      label = `${dateUtil.getYear(updateDay)}年`;
    }

    return {
      ...it,
      timeLabel: label,
      timeYmd: dateUtil.formatYMD(updateDate),
      timeHm: dateUtil.formatHM(updateDate),
    };
  });
};

// 按时间标签分组
const groupByTimeLabel = (data: ChatMessage[]) => {
  const groupedData: ChatGroup[] = [];
  data.forEach(item => {
    const existingGroup = groupedData.find(group => group.timeLabel === item.timeLabel);
    if (existingGroup) {
      existingGroup.messages.push(item);
    } else {
      groupedData.push({
        timeLabel: item.timeLabel || '',
        messages: [item],
      });
    }
  });
  return groupedData;
};

// 点击会话项跳转到聊天详情
const chatDetail = (message: ChatMessage) => {
  const defaultAppObj = uni.getStorageSync('defaultApp');
  const defaultAppId = defaultAppObj ? defaultAppObj.id : '';
  console.log('defaultAppId', defaultAppId);
  console.log('message.tenantAppId', message.tenantAppId);
  if (message.tenantAppId === defaultAppId) {
    uni.setStorageSync('oldChatId', message.chatId);
    uni.switchTab({
      url: `/pages/chat/index`,
    });
  } else {
    uni.navigateTo({
      url: `/pages/chat/index?chatType=3&chatId=${message.chatId}&appId=${message.tenantAppId}&name=${message.title}&appAvatarUrl=${message.appAvatarUrl}`,
    });
  }
};

// "我的常用"按钮点击事件处理
const handleFavorites = () => {
  addFavorites.value = !addFavorites.value;
  if (addFavorites.value) {
    userCommonApi.setCommonApp({ id: appDetail.value?.id || '' }).then(() => {
      uToastRef.value.show({
        message: '已添加到我的应用',
        type: 'success',
      });
    });
  } else {
    userCommonApi.rmCommonApp({ id: appDetail.value?.id || '' }).then(() => {
      uToastRef.value.show({
        message: '已从我的应用中移除',
        type: 'success',
      });
    });
  }
};

// "分享应用"按钮点击事件处理
const handleShareApp = async () => {
  console.log('[Share] 开始分享应用');
  showSharePopup.value = true;

  // 重置错误状态
  qrCodeError.value = false;

  // 确保应用详情已加载
  if (!appDetail.value) {
    console.error('[Share] 应用详情未加载');
    return;
  }

  try {
    // 重新生成二维码值
    const shareLink = await generateAppShareLink(appDetail.value);
    qrCodeValue.value = shareLink;
    console.log('[Share] 二维码链接已生成:', shareLink);

    // 延迟确保DOM更新完成
    setTimeout(() => {
      console.log('[Share] 延迟后检查二维码组件状态');
    }, 100);
  } catch (error) {
    console.error('[Share] 生成分享链接失败:', error);
    qrCodeError.value = true;
  }
};

const closeSharePopup = () => {
  showSharePopup.value = false;
};

interface ShareCallbackResult {
  event: string;
  index?: number;
}

const shareTo = async (platform: string) => {
  console.log('分享到:', platform);

  if (!appDetail.value) {
    uni.showToast({ title: '应用详情加载中，请稍候', icon: 'none' });
    return;
  }

  try {
    // 先生成分享图片
    uni.showLoading({ title: '准备分享...', mask: true });

    // 确保二维码已经完全准备好
    await ensureQrCodeReady();

    // 使用完整的 generateAndSaveShareImage 逻辑生成图片但不保存到相册
    const shareImagePath = await generateShareImageForShare();
    shareImageUrl.value = shareImagePath;

    uni.hideLoading();

    // 直接调用微信分享，不显示菜单
    if (platform === 'moments') {
      // 朋友圈分享
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneTimeline',
        type: 2, // 图片分享
        imageUrl: shareImagePath,
        title: appDetail.value?.name || '应用分享',
        summary: appDetail.value?.intro || '扫描二维码，体验应用',
        success: ret => {
          closeSharePopup();
          uni.showToast({ title: '分享成功', icon: 'success' });
        },
        fail: err => {
          uni.showToast({ title: '分享失败，请重试 ' + JSON.stringify(err), icon: 'none' });
        },
      });
    } else if (platform === 'wechat') {
      // 微信好友分享
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 2, // 图片分享
        imageUrl: shareImagePath,
        title: appDetail.value?.name || '应用分享',
        summary: appDetail.value?.intro || '扫描二维码，体验应用',
        success: ret => {
          console.log('分享给微信好友成功', JSON.stringify(ret));
          closeSharePopup();
          uni.showToast({ title: '分享成功', icon: 'success' });
        },
        fail: err => {
          console.error('分享给微信好友失败', err);
          uni.showToast({ title: '分享失败，请重试 ' + JSON.stringify(err), icon: 'none' });
        },
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error('分享失败:', error);
    uni.showToast({
      title: '分享准备失败，请重试 ' + JSON.stringify(error),
      icon: 'none',
    });
  }
};

// 确保二维码准备就绪的函数
const ensureQrCodeReady = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    let retryCount = 0;
    const maxRetries = 15; // 减少重试次数

    const checkQrCode = async () => {
      try {
        // 如果二维码URL已经存在，直接返回
        if (qrCodeImageUrl.value) {
          resolve();
          return;
        }
        retryCount++;
        if (retryCount >= maxRetries) {
          console.warn('二维码准备超时，继续执行，重试次数:', retryCount);
          resolve(); // 即使没有二维码也继续执行
          return;
        }
        setTimeout(checkQrCode, 500); // 增加重试间隔
      } catch (error) {
        console.error('检查二维码状态失败:', error);
        retryCount++;
        if (retryCount >= maxRetries) {
          console.warn('二维码检查失败超过最大重试次数，继续执行');
          resolve(); // 即使失败也继续执行
        } else {
          setTimeout(checkQrCode, 500);
        }
      }
    };

    // 开始检查
    checkQrCode();
  });
};

const saveToAlbum = async () => {
  if (!appDetail.value) {
    uni.showToast({ title: '应用详情加载中，请稍候', icon: 'none' });
    return;
  }

  uni.showLoading({ title: '准备中...', mask: true });

  try {
    // 确保二维码已经完全准备好
    await ensureQrCodeReady();

    // 使用与分享功能相同的图片生成方法
    const shareImagePath = await generateShareImageForShare();

    // 保存图片到相册
    uni.saveImageToPhotosAlbum({
      filePath: shareImagePath,
      success: () => {
        uni.hideLoading();
        uni.showToast({ title: '已保存到相册', icon: 'success' });
        showSharePopup.value = false;
      },
      fail: err => {
        uni.hideLoading();
        console.error('保存图片到相册失败:', err);
        uni.showToast({ title: '保存失败，请重试', icon: 'none' });
      },
    });
  } catch (error) {
    uni.hideLoading();
    uni.showToast({ title: '保存失败，请重试', icon: 'none' });
    console.error('保存到相册失败:', error);
  }
};

// 格式化应用介绍文字，确保不会超出
const formattedIntro = computed(() => {
  const intro = appDetail.value?.intro || '暂无介绍';
  if (intro.length > 100) {
    return intro.substring(0, 97) + '...';
  }
  return intro;
});

// 生成分享图片（不保存到相册，返回图片路径）
const generateShareImageForShare = async (): Promise<string> => {
  if (!appDetail.value) {
    throw new Error('应用详情加载中，请稍候');
  }

  // 处理应用介绍文字，确保不会过长
  let introText = appDetail.value?.intro || '';
  if (introText.length > 100) {
    introText = introText.substring(0, 97) + '...';
  }

  return new Promise(async (resolve, reject) => {
    const ctx = uni.createCanvasContext('shareCanvas', proxy);

    if (canvasWidthPxComputed.value <= 0 || canvasHeightPxComputed.value <= 0) {
      reject(new Error('画布尺寸错误'));
      return;
    }

    try {
      // 使用与 generateAndSaveShareImage 相同的完整绘制逻辑
      ctx.setFillStyle('#FFFFFF');
      ctx.fillRect(0, 0, canvasWidthPxComputed.value, canvasHeightPxComputed.value);

      // 1. 下载并绘制主背景图
      const dialogBackgroundImageUrl =
        'https://huayun-ai-obs-public.huayuntiantu.com/7f39f1f14f2d4e1df6c04404d2140807.png';

      try {
        const localBgPath = await new Promise<string>((resolve, reject) => {
          uni.downloadFile({
            url: dialogBackgroundImageUrl,
            success: res => {
              if (res.statusCode === 200 && res.tempFilePath) {
                resolve(res.tempFilePath);
              } else {
                reject(new Error(`下载背景图失败: ${res.statusCode}`));
              }
            },
            fail: err => reject(err),
          });
        });

        // 获取背景图信息并绘制
        const bgImgInfo = await uni.getImageInfo({ src: localBgPath });
        const dialogCardVisualOffsetY = uni.upx2px(60);
        const dialogCardVisualWidth = canvasWidthPxComputed.value;
        const dialogCardVisualHeight = uni.upx2px(824);

        const imgOriginalWidth = bgImgInfo.width;
        const imgOriginalHeight = bgImgInfo.height;
        const aspectRatio = imgOriginalWidth / imgOriginalHeight;

        let bgDrawWidth = dialogCardVisualWidth;
        let bgDrawHeight = dialogCardVisualWidth / aspectRatio;
        if (bgDrawHeight > dialogCardVisualHeight) {
          bgDrawHeight = dialogCardVisualHeight;
          bgDrawWidth = dialogCardVisualHeight * aspectRatio;
        }
        const bgDrawX = (dialogCardVisualWidth - bgDrawWidth) / 2;
        const bgDrawY = dialogCardVisualOffsetY + (dialogCardVisualHeight - bgDrawHeight) / 2;

        ctx.drawImage(localBgPath, bgDrawX, bgDrawY, bgDrawWidth, bgDrawHeight);
      } catch (bgError) {
        console.log('背景图加载失败，使用渐变背景');
        // 如果背景图加载失败，使用渐变色背景
        ctx.setFillStyle('#7d4dff');
        const bgHeight = canvasHeightPxComputed.value * 0.7;
        ctx.fillRect(0, 0, canvasWidthPxComputed.value, bgHeight);
      }

      // 2. 绘制顶部应用图标
      const logoContainerDiameterPx = uni.upx2px(132);
      const logoIconSizePx = uni.upx2px(70);
      const logoContainerCenterX = canvasWidthPxComputed.value / 2;
      const logoContainerCenterY = logoContainerDiameterPx / 2;

      // 绘制白色圆形背景
      ctx.save();
      ctx.beginPath();
      ctx.arc(
        logoContainerCenterX,
        logoContainerCenterY,
        logoContainerDiameterPx / 2,
        0,
        2 * Math.PI
      );
      ctx.setFillStyle('#FFFFFF');
      ctx.fill();
      ctx.restore();

      // 尝试绘制应用图标
      if (appDetail.value && appDetail.value.avatarUrl) {
        try {
          const localIconPath = await new Promise<string>((resolve, reject) => {
            uni.downloadFile({
              url: appDetail.value!.avatarUrl,
              success: res => {
                if (res.statusCode === 200 && res.tempFilePath) {
                  resolve(res.tempFilePath);
                } else {
                  reject(new Error(`下载图标失败: ${res.statusCode}`));
                }
              },
              fail: err => reject(err),
            });
          });

          ctx.drawImage(
            localIconPath,
            logoContainerCenterX - logoIconSizePx / 2,
            logoContainerCenterY - logoIconSizePx / 2,
            logoIconSizePx,
            logoIconSizePx
          );
        } catch (iconError) {
          console.log('应用图标加载失败，使用默认图标');
          // 如果应用图标加载失败，绘制默认图标
          ctx.setFillStyle('#7d4dff');
          ctx.fillRect(
            logoContainerCenterX - logoIconSizePx / 2,
            logoContainerCenterY - logoIconSizePx / 2,
            logoIconSizePx,
            logoIconSizePx
          );
        }
      }

      // 3. 绘制应用名称
      const mainTitleY = uni.upx2px(60) + uni.upx2px(80) + uni.upx2px(36);
      ctx.setFillStyle('#303133');
      ctx.setFontSize(uni.upx2px(36));
      ctx.setTextAlign('center');
      ctx.fillText(appDetail.value?.name || '应用分享', logoContainerCenterX, mainTitleY);

      // 4. 绘制应用描述
      const subTitleY = mainTitleY + uni.upx2px(40);
      ctx.setFillStyle('#606266');
      ctx.setFontSize(uni.upx2px(24));
      ctx.setTextAlign('center');

      // 处理长文本换行
      const maxWidth = uni.upx2px(500);
      const lines = [];
      let currentLine = '';

      for (let char of introText) {
        const testLine = currentLine + char;
        const testWidth = ctx.measureText(testLine).width;
        if (testWidth > maxWidth && currentLine !== '') {
          lines.push(currentLine);
          currentLine = char;
        } else {
          currentLine = testLine;
        }
      }
      if (currentLine) lines.push(currentLine);

      // 绘制最多2行文字
      const maxLines = Math.min(lines.length, 2);
      for (let i = 0; i < maxLines; i++) {
        ctx.fillText(lines[i], logoContainerCenterX, subTitleY + i * uni.upx2px(32));
      }

      // 5. 绘制二维码区域
      const qrBgSize = uni.upx2px(280);
      const qrBgX = (canvasWidthPxComputed.value - qrBgSize) / 2;
      const qrBgY = subTitleY + uni.upx2px(80);

      // 绘制二维码白色背景
      ctx.setFillStyle('#FFFFFF');
      ctx.fillRect(qrBgX, qrBgY, qrBgSize, qrBgSize);

      // 绘制二维码
      console.log('准备绘制二维码，qrCodeImageUrl:', qrCodeImageUrl.value);
      if (qrCodeImageUrl.value) {
        try {
          // 如果二维码URL是base64格式，直接使用
          let qrCodePath = qrCodeImageUrl.value;

          // 如果是http/https URL，需要下载
          if (qrCodeImageUrl.value.startsWith('http')) {
            qrCodePath = await new Promise<string>((resolve, reject) => {
              uni.downloadFile({
                url: qrCodeImageUrl.value,
                success: res => {
                  if (res.statusCode === 200 && res.tempFilePath) {
                    resolve(res.tempFilePath);
                  } else {
                    console.warn('下载二维码失败，使用原URL');
                    resolve(qrCodeImageUrl.value);
                  }
                },
                fail: () => {
                  console.warn('下载二维码失败，使用原URL');
                  resolve(qrCodeImageUrl.value);
                },
              });
            });
          }

          const qrSize = qrBgSize * 0.85;
          const qrX = qrBgX + (qrBgSize - qrSize) / 2;
          const qrY = qrBgY + (qrBgSize - qrSize) / 2;

          console.log('绘制二维码:', qrCodePath, qrX, qrY, qrSize);
          ctx.drawImage(qrCodePath, qrX, qrY, qrSize, qrSize);
        } catch (qrError) {
          console.log('二维码绘制失败:', qrError);
          // 绘制占位符
          ctx.setFillStyle('#CCCCCC');
          const qrSize = qrBgSize * 0.8;
          const qrX = qrBgX + (qrBgSize - qrSize) / 2;
          const qrY = qrBgY + (qrBgSize - qrSize) / 2;
          ctx.fillRect(qrX, qrY, qrSize, qrSize);

          // 绘制占位符文字
          ctx.setFillStyle('#666666');
          ctx.setFontSize(uni.upx2px(20));
          ctx.setTextAlign('center');
          ctx.fillText('二维码', qrBgX + qrBgSize / 2, qrBgY + qrBgSize / 2);
        }
      } else {
        console.warn('没有二维码图片，绘制占位符');
        // 绘制占位符
        ctx.setFillStyle('#CCCCCC');
        const qrSize = qrBgSize * 0.8;
        const qrX = qrBgX + (qrBgSize - qrSize) / 2;
        const qrY = qrBgY + (qrBgSize - qrSize) / 2;
        ctx.fillRect(qrX, qrY, qrSize, qrSize);

        // 绘制占位符文字
        ctx.setFillStyle('#666666');
        ctx.setFontSize(uni.upx2px(20));
        ctx.setTextAlign('center');
        ctx.fillText('二维码', qrBgX + qrBgSize / 2, qrBgY + qrBgSize / 2);
      }

      // 6. 绘制扫码提示文字
      const scanTextY = qrBgY + qrBgSize + uni.upx2px(30);
      ctx.setFillStyle('#1D2129');
      ctx.setFontSize(uni.upx2px(24));
      ctx.setTextAlign('center');
      ctx.fillText('扫描二维码，体验应用', logoContainerCenterX, scanTextY);

      // 7. 绘制底部logo
      try {
        const localLogoPath = await new Promise<string>((resolve, reject) => {
          uni.downloadFile({
            url: logoUrl.value,
            success: res => {
              if (res.statusCode === 200 && res.tempFilePath) {
                resolve(res.tempFilePath);
              } else {
                reject(new Error(`下载logo失败: ${res.statusCode}`));
              }
            },
            fail: err => reject(err),
          });
        });

        const bottomLogoWidthPx = uni.upx2px(113.756);
        const bottomLogoHeightPx = uni.upx2px(46);
        const bottomLogoY = scanTextY + uni.upx2px(40);

        ctx.drawImage(
          localLogoPath,
          (canvasWidthPxComputed.value - bottomLogoWidthPx) / 2,
          bottomLogoY,
          bottomLogoWidthPx,
          bottomLogoHeightPx
        );
      } catch (logoError) {
        console.log('底部logo加载失败');
      }

      ctx.draw(false);

      setTimeout(() => {
        uni.canvasToTempFilePath(
          {
            x: 0,
            y: 0,
            width: canvasWidthPxComputed.value,
            height: canvasHeightPxComputed.value,
            destWidth: canvasWidthPxComputed.value * dpr,
            destHeight: canvasHeightPxComputed.value * dpr,
            canvasId: 'shareCanvas',
            fileType: 'png',
            quality: 1.0,
            success: res => {
              if (res.tempFilePath) {
                console.log('分享图片生成成功:', res.tempFilePath);
                resolve(res.tempFilePath);
              } else {
                reject(new Error('生成图片失败'));
              }
            },
            fail: err => {
              console.error('生成图片失败:', err);
              reject(err);
            },
          },
          proxy
        );
      }, 1500); // 增加延迟确保绘制完成
    } catch (error) {
      console.error('绘制过程出错:', error);
      reject(error);
    }
  });
};
</script>

<style lang="scss" scoped>
.share-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/329fcfe0-66b5-464d-8170-460b809be551.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.nav-bar {
  padding-top: var(--status-bar-height);
  height: 88rpx;
  display: flex;
  align-items: center;
  padding-left: 30rpx; // 返回按钮的左边距
  flex-shrink: 0;
}

.scroll-content {
  flex: 1;
  overflow-y: auto;
}

.content-wrapper {
  padding: 0 40rpx 40rpx; // 左右及底部内边距
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx; // 与导航栏的间距
  margin-bottom: 50rpx;
  text-align: center;

  .logo-image-container {
    width: 132rpx;
    height: 132rpx;
    border-radius: 50%;
    background-color: #ffffff;
    border: 3px solid #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;
    overflow: hidden; // 确保图片在圆形内
  }

  .logo-icon {
    width: 90rpx; // 图标实际大小
    height: 90rpx;
  }

  .main-title {
    font-size: 40rpx;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20rpx;
  }

  .sub-title {
    font-size: 24rpx;
    color: #606266;
    line-height: 1.5;
    max-width: 600rpx; // 控制文字宽度，使其换行
    margin: 0 32rpx;
    display: -webkit-box; /* 必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 */
    -webkit-line-clamp: 3; /* 限制在一个块元素显示的文本的行数 */
    -webkit-box-orient: vertical; /* 必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;

  .btn {
    width: calc(50% - 15rpx); // 计算按钮宽度，15rpx为按钮间距的一半
    border-radius: 16rpx; // 圆角
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx;
    box-sizing: border-box;
    border: none;
    // 移除默认的 ::after 边框 (在某些平台按钮可能会有)
    &::after {
      border: none;
    }
  }
  .favorites-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    background-color: #ffffff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .favorites-btn {
    background-color: #7d4dff;
    color: #ffffff;
    &.favorited {
      background-color: #b694ff;
    }
  }

  .share-btn {
    background-color: #f3f3f3;
    color: #1d2129;
  }
}

.history-section {
  margin-bottom: 50rpx;

  .history-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 24rpx;
    display: block;
  }

  .search-bar {
    display: flex;
    align-items: center;
    background-color: #f3f3f3;
    border-radius: 16rpx; // 圆角
    padding: 0 25rpx;
    height: 80rpx;
    margin-bottom: 30rpx;

    .search-input {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      margin-left: 15rpx;
      height: 100%;
      background-color: transparent; // 确保input背景透明
      border: none; // 移除默认边框
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 40rpx; // 与上方元素的间距

  .empty-icon {
    width: 280rpx;
    height: 230rpx;
    margin-bottom: 30rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #4e5969;
  }
}

.divider-line {
  width: 100%;
  height: 1px;
  background-color: #f3f3f3;
  margin: 40rpx 0;
}

.chat-content {
  max-height: 700rpx;
  overflow-y: auto;
}

.chat-day {
  margin-bottom: 30rpx;
}

.chat-day-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #909399;
  margin-bottom: 20rpx;
}

.chat-message {
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.share-dialog {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS >= 11.2 */
}

.share-dialog-centered {
  width: v-bind('shareDialogDimensions.width + "px"');
  height: v-bind('shareDialogDimensions.height + "px"');
  background-image: url('https://huayun-ai-obs-public.huayuntiantu.com/7f39f1f14f2d4e1df6c04404d2140807.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  margin-bottom: 80px;
  padding: 0;
  box-sizing: border-box;

  .logo-image-container {
    width: v-bind('shareDialogDimensions.logoSize + "px"');
    height: v-bind('shareDialogDimensions.logoSize + "px"');
    border-radius: 50%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: v-bind('"-" + (shareDialogDimensions.logoSize / 2) + "px"');
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    margin-bottom: 0;
  }

  .logo-icon {
    width: v-bind('shareDialogDimensions.logoIconSize + "px"');
    height: v-bind('shareDialogDimensions.logoIconSize + "px"');
  }

  .dalog-main-title {
    font-size: v-bind('shareDialogDimensions.titleFontSize + "px"');
    font-weight: 500;
    color: #303133;
    text-align: center;
    margin-top: v-bind('(shareDialogDimensions.logoSize / 2 + 20) + "px"');
    margin-bottom: 0;
  }
  .dalog-sub-title {
    font-size: v-bind('shareDialogDimensions.subtitleFontSize + "px"');
    color: #606266;
    line-height: 1.5;
    max-width: v-bind('(shareDialogDimensions.width * 0.85) + "px"');
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 10px 0;
    text-align: center;
  }
  .share-dialog-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 28px;
    width: v-bind('(shareDialogDimensions.width * 0.92) + "px"');
    height: v-bind('(shareDialogDimensions.height * 0.72) + "px"');
    border-radius: 13px;
    border: 1px solid #ffffff;
    background: rgba(255, 255, 255, 0.32);
    backdrop-filter: blur(1.9px);
    margin-bottom: 20px;
    .share-dialog-qrcode {
      display: flex;
      justify-content: center;
      align-items: center;
      width: v-bind('shareDialogDimensions.qrCodeSize + "px"');
      height: v-bind('shareDialogDimensions.qrCodeSize + "px"');
      border-radius: 11px;
      background: #ffffff;

      .qrcode-container {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .qrcode-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .error-text {
          font-size: v-bind('shareDialogDimensions.subtitleFontSize + "px"');
          color: #999999;
          margin-bottom: 10px;
        }

        .retry-btn {
          padding: 5px 10px;
          background: #7d4dff;
          color: #ffffff;
          border: none;
          border-radius: 4px;
          font-size: v-bind('(shareDialogDimensions.subtitleFontSize - 1) + "px"');
          &::after {
            border: none;
          }
        }
      }

      .qrcode-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: v-bind('shareDialogDimensions.subtitleFontSize + "px"');
        color: #999999;
      }
    }
    .share-dialog-qrcode-logo {
      width: v-bind('shareDialogDimensions.bottomLogoWidth + "px"');
      height: v-bind('shareDialogDimensions.bottomLogoHeight + "px"');
    }
    .share-dialog-qrcode-text {
      font-size: v-bind('shareDialogDimensions.qrCodeTextSize + "px"');
      color: #1d2129;
      margin-top: 8px;
      margin-bottom: 27px;
    }
  }
}

.share-actions-bottom-panel {
  background-color: #ffffff;
  width: 100%;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  padding-top: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.share-options-grid {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  padding: 0 15px;
  margin-bottom: 20px;
}

.option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;

  .icon-wrapper {
    width: v-bind('(shareDialogDimensions.iconSize + 10) + "px"');
    height: v-bind('(shareDialogDimensions.iconSize + 10) + "px"');
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
  }

  .option-icon {
    width: v-bind('shareDialogDimensions.iconSize + "px"');
    height: v-bind('shareDialogDimensions.iconSize + "px"');
  }

  .option-text {
    font-size: v-bind('shareDialogDimensions.optionTextSize + "px"');
    color: #303133;
  }
}

.actions-panel-divider {
  height: 1px;
  background-color: #e5e7eb;
  width: 100%;
}

.cancel-row {
  padding: 15px 0;
  text-align: center;
  width: 100%;

  .cancel-text-label {
    font-size: v-bind('(shareDialogDimensions.titleFontSize - 2) + "px"');
    color: #303133;
  }
}
</style>
