<template>
  <view v-if="visible" class="slide-code">
    <view class="slide-verify" :style="{ width: 'calc(100% - 6rpx)', margin: '0 auto' }">
      <!-- 头部 -->
      <view class="header">
        <view class="title">滑动校验</view>
        <view class="close" @click="handleClose">
          <u-icon name="close" color="#bbbbbb" size="32rpx"></u-icon>
        </view>
      </view>

      <!-- 提示文本 -->
      <view class="msg-text">{{ msgText }}</view>

      <!-- 验证码区域 -->
      <view class="verify-area">
        <view class="canvas-wrap">
          <view class="canvas-container" :style="containerStyle">
            <image
              v-if="captchaInfo.canvasSrc"
              :src="captchaInfo.canvasSrc"
              :style="imageStyle"
              class="canvas-img"
              mode="aspectFit"
            />
            <view :class="['block', { shake: isShaking }]" :style="blockStyle"></view>
          </view>

          <!-- 滑块区域 -->
          <view class="slider-container" :style="{ width: `${captchaInfo.canvasWidth}px` }">
            <movable-area class="slider-area">
              <movable-view
                class="slider-view"
                direction="horizontal"
                :x="sliderValue"
                :disabled="isLoading"
                @change="onSliderMove"
                @touchend="onSliderEnd"
                @mouseup="onSliderEnd"
                @mouseleave="onSliderEnd"
              >
                <view class="slider-btn">
                  <u-icon name="arrow-right" size="28rpx" color="#666666"></u-icon>
                </view>
              </movable-view>
              <view class="slider-track">向右滑动完成验证</view>
            </movable-area>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { getCaptcha, validCaptcha } from '@/api/auth';

interface CaptchaInfo {
  canvasWidth: number;
  canvasHeight: number;
  blockWidth: number;
  blockHeight: number;
  blockRadius: number;
  blockX: number;
  blockY: number;
  canvasSrc: string;
  blockSrc: string;
  ticket: string;
}

// 状态定义
const visible = ref(false);
const msgText = ref('请将滑块拖动到正确位置');
const sliderValue = ref(0);
const isShaking = ref(false);
const isLoading = ref(false);
const beforeBlockX = ref(0);

// Promise解析器
let resolvePromise: ((value: any) => void) | null = null;
let rejectPromise: ((reason?: any) => void) | null = null;
const initData = {
  canvasWidth: 322,
  canvasHeight: 155,
  blockWidth: 0,
  blockHeight: 0,
  blockRadius: 0,
  blockX: 0,
  blockY: 0,
  canvasSrc: '',
  blockSrc: '',
  ticket: '',
};
// 验证码信息
const captchaInfo = reactive<CaptchaInfo>({ ...initData });

// 计算属性
const containerStyle = computed(() => ({
  position: 'relative' as const,
  width: `${captchaInfo.canvasWidth}px`,
  height: `${captchaInfo.canvasHeight}px`,
}));

const imageStyle = computed(() => ({
  position: 'relative' as const,
  width: '100%',
  height: '100%',
}));

const blockStyle = computed(() => ({
  position: 'absolute' as const,
  left: `${captchaInfo.blockX}px`,
  top: `${captchaInfo.blockY}px`,
  width: `${captchaInfo.blockWidth}px`,
  height: `${captchaInfo.blockHeight}px`,
  borderRadius: `${captchaInfo.blockRadius}px`,
  background: `url(${captchaInfo.blockSrc})`,
  backgroundSize: '100% 100%',
}));

// 方法定义
const refresh = async () => {
  try {
    isLoading.value = true;
    const res = await getCaptcha({});
    Object.assign(captchaInfo, res);
    captchaInfo.blockX = 0;
    sliderValue.value = 0;
  } catch (error: any) {
    msgText.value = error.message || '加载失败';
  } finally {
    isLoading.value = false;
  }
};

const reset = () => {
  captchaInfo.blockX = 0;
  sliderValue.value = 0;
};

const handleClose = () => {
  Object.assign(captchaInfo, { ...initData });

  visible.value = false;
  rejectPromise?.();
};

const onSliderMove = (e: any) => {
  const { x } = e.detail;
  sliderValue.value = x;
  captchaInfo.blockX = x;
};

const onSliderEnd = async () => {
  beforeBlockX.value = captchaInfo.blockX;

  try {
    msgText.value = '验证中...';
    const params = {
      moveLength: Number(captchaInfo.blockX.toFixed(0)),
      ticket: captchaInfo.ticket,
    };

    await validCaptcha(params);
    msgText.value = '验证通过';
    visible.value = false;
    reset();
    resolvePromise?.(params);
  } catch (error) {
    isShaking.value = true;
    setTimeout(() => {
      isShaking.value = false;
    }, 1000);

    reset();
    msgText.value = '验证失败，请重试';
    refresh();
  }
};

// 对外暴露的方法
const open = () => {
  visible.value = true;
  msgText.value = '请将滑块拖动到正确位置';
  reset();
  refresh();

  return new Promise((resolve, reject) => {
    resolvePromise = resolve;
    rejectPromise = reject;
  });
};

// 暴露方法给父组件
defineExpose({
  open,
  reset,
});
</script>

<style lang="scss">
.slide-code {
  display: flex;
  background: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  width: 750rpx;
  height: 100vh;
  justify-content: center;
  align-items: center;

  .slide-verify {
    position: relative;
    flex-direction: column;
    background: #fff;
    padding: 20rpx 25rpx 30rpx 25rpx;
    border-radius: 15rpx;
    width: 90%;
    max-width: 650rpx;

    .header {
      height: 50rpx;
      justify-content: space-between;
      align-items: center;
      display: flex;
      padding: 30rpx;

      .title {
        font-size: 38rpx;
        font-weight: 600;
      }

      .close {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        padding: 10rpx;
      }
    }

    .msg-text {
      padding-left: 20rpx;
      margin-bottom: 10rpx;
      font-size: 28rpx;
      color: #666;
    }

    .verify-area {
      width: 100%;
      display: flex;
      justify-content: center;

      .canvas-wrap {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .canvas-container {
          overflow: hidden;
          background: #f7f9fa;
          border-radius: 8rpx;
          margin: 0 auto;

          .canvas-img {
            display: block;
            width: 100% !important;
            height: 100% !important;
          }

          .block {
            transition: transform 0.3s;
            background-size: 100% 100% !important;

            &.shake {
              animation: shake 0.5s linear;
            }
          }
        }

        .slider-container {
          margin-top: 20rpx;
          width: 100%;
          .slider-btn {
            z-index: 10000;
          }
          .slider-area {
            position: relative;
            width: 100%;
            height: 80rpx;
            background: #f7f9fa;
            border-radius: 40rpx;

            .slider-view {
              width: 70rpx;
              height: 70rpx;
              z-index: 10000;
              background: #fff;
              border-radius: 50%;
              box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
              display: flex;
              align-items: center;
              justify-content: center;
              position: absolute;
              top: 5rpx;
              left: 5rpx;
            }

            .slider-track {
              position: absolute;
              left: 90rpx;
              top: 50%;
              transform: translateY(-50%);
              color: #999;
              font-size: 28rpx;
            }
          }
        }
      }
    }
  }
}

@keyframes shake {
  20% {
    transform: translateX(-15px);
  }
  40% {
    transform: translateX(15px);
  }
  60% {
    transform: translateX(-15px);
  }
  80% {
    transform: translateX(15px);
  }
  100% {
    transform: translateX(0);
  }
}
</style>
