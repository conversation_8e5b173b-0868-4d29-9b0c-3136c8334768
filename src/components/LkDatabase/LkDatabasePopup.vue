<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import LkSvg from '@/components/svg/index.vue';
import LkDatabaseItem from '@/components/LkDatabase/LkDatabaseItem.vue';
import LkDatabasePath from '@/components/LkDatabase/LkDatabasePath.vue';
import LkSearch from '@/components/LkSearch/index.vue';
import { addFromMyself } from '@/api/database';
import { onLoad } from '@dcloudio/uni-app';

type NavOptions = {
  id: string;
  navTitle: string;
  bizType: string;
};

const props = defineProps<{
  bizType: string;
  layoutType: number;
  preSelectedIds?: string[];
  fileObj?: any;
}>();

const emit = defineEmits(['confirmRecovery', 'confirmSelectedItems', 'confirmMove']);

const isPopup = ref(false);
const searchValue = ref('');

// 监听预选ID变化
watch(
  () => props.preSelectedIds,
  newIds => {
    if (isPopup.value && newIds && newIds.length > 0 && databaseItemRef.value) {
      // 设置预选ID到数据库项组件
      databaseItemRef.value.setPreSelectedIds(newIds);
    }
  }
);

const openPopup = () => {
  isPopup.value = true;
  // 重置选中状态，但保留已预选的项
  if (props.preSelectedIds && props.preSelectedIds.length > 0) {
    // 延迟设置预选项，确保组件已经挂载
    setTimeout(() => {
      if (databaseItemRef.value) {
        databaseItemRef.value.setPreSelectedIds(props.preSelectedIds);
      }
    }, 100);
  } else {
    selectedItems.value = [];
  }
};

const closePopup = () => {
  isPopup.value = false;
  // 清空路径数据
  pathList.value = [];
  // 不清空选中项，保留当前选择状态
};

defineExpose({
  openPopup,
});

const pathList = ref<any[]>([]);
const databaseItemRef = ref<any>(null);
const selectedItems = ref<any[]>([]);
const urlOptions = ref<NavOptions>();
const currentParentObj = ref<any>({});

onLoad(options => {
  urlOptions.value = { ...options } as NavOptions;
});

const updatePathData = (pathData: any) => {
  console.log(pathData);
  // pathList.value.push({
  //   spaceName: pathData?.spaceName,
  //   id: pathData?.id,
  // });
  pathList.value.push(pathData);
  currentParentObj.value = pathData;
};
const updateSpaceData = (spaceData: any) => {
  console.log(spaceData);
  console.log(databaseItemRef.value.currentParentId);
  databaseItemRef.value.currentParentId = spaceData?.id || '0';
  currentParentObj.value = spaceData;
};
const clickSave = () => {
  console.log(databaseItemRef.value.currentParentId);
  if (props.layoutType === 4) {
    emit('confirmRecovery', databaseItemRef.value.currentParentId);
  } else if (props.layoutType === 5) {
    // 移动文件/文件夹
    console.log('移动文件/文件夹');
    emit('confirmMove', {
      // 移动到的obj数据
      currentParentObj: currentParentObj.value,
      // 被移动的obj
      fileObj: props?.fileObj,
    });
  }
};

// 更新选中的项
const updateSelectedItems = (items: any[]) => {
  selectedItems.value = items;
  console.log('已选中的项:', items);
  if (props.layoutType === 2) {
    // 更新选中计数
    selectedCount.value = items.length;

    // 根据是否所有可选文件都被选中来更新全选状态
    // 只有在有文件且全部被选中时才是全选状态
    console.log('databaseItemRef:', databaseItemRef.value);
    if (databaseItemRef.value && databaseItemRef.value.currentList) {
      try {
        const selectableItems = databaseItemRef.value.currentList.filter(
          (item: any) => item.fileType === 1
        );
        const selectableCount = selectableItems.length;
        console.log('可选项目数量:', selectableCount, '已选项目数量:', items.length);
        isSelectAll.value = selectableCount > 0 && items.length === selectableCount;
      } catch (error) {
        console.error('计算全选状态出错:', error);
      }
    }
  }
};

// 搜索处理函数
const handleSearch = (value: string) => {
  console.log('搜索关键词:', value);
  if (databaseItemRef.value) {
    databaseItemRef.value.setSearchKeywordAndRefresh(value);
  }
};

// 清除搜索
const handleClear = () => {
  console.log('清除搜索');
  searchValue.value = '';
  if (databaseItemRef.value) {
    databaseItemRef.value.setSearchKeywordAndRefresh('');
  }
};

const saveToSpace = () => {
  console.log('保存到数据空间');
  addFromMyself({
    // 当前所在的学校数据空间id,从当前url参数取
    spaceId: urlOptions.value?.id,
    // 已选中项的id
    sourceFileIds: selectedItems.value.map(item => item.id),
    isAuditRoot: 1,
  }).then(res => {
    console.log(res);
    uni.showToast({
      title: '添加成功',
      icon: 'success',
    });
    closePopup();
    databaseItemRef.value.refreshPageList();
  });
};

const handleSave = () => {
  console.log('选中的项ID:', selectedItems.value);

  if (selectedItems.value.length === 0) {
    uni.showToast({
      title: '请至少选择一个文件',
      icon: 'none',
    });
    return;
  }

  // 处理选中的文件，转换为LKUploadall需要的格式
  const processedItems = selectedItems.value.map(item => {
    return {
      fileUrl: item.file?.fileUrl || item.fileUrl,
      fileName: item.fileName,
      fileKey: item.file?.fileKey || item.fileKey,
      id: item.id,
      fileType: item.fileType,
      fileSize: item.fileSize || item.file?.fileSize,
      type: 'file',
      uploadTime: Date.now(),
    };
  });

  console.log('processedItems', processedItems);
  emit('confirmSelectedItems', processedItems);
  closePopup();
};

const handleSelectAll = () => {
  console.log('全选');
  isSelectAll.value = !isSelectAll.value;
  console.log(isSelectAll.value);

  // 调用子组件的toggleCheckAll方法
  if (databaseItemRef.value) {
    databaseItemRef.value.toggleCheckAll();
  }
};

const isSelectAll = ref(false);
const selectedCount = ref(0);
</script>
<template>
  <view class="lk-database">
    <up-popup :show="isPopup" mode="bottom" @close="closePopup" :safeAreaInsetBottom="true">
      <view class="wrapMain">
        <!-- header -->
        <view class="lk-database-header">
          <text class="title">{{ bizType === '1' ? '学校数据空间' : '我的数据空间' }}</text>
          <LkSvg
            class="close-btn"
            width="24px"
            height="24px"
            src="/static/database/close.svg"
            @click="closePopup"
          />
        </view>
        <!-- search -->
        <view v-if="layoutType === 2 || layoutType === 3" class="lk-database-search">
          <LkSearch
            v-model="searchValue"
            placeholder="请输入关键词搜索"
            @search="handleSearch"
            @clear="handleClear"
          />
        </view>
        <!-- main -->
        <view class="lk-database-main">
          <!-- path -->
          <LkDatabasePath
            v-model="pathList"
            :bizType="bizType"
            @updateSpaceData="updateSpaceData"
          />
          <scroll-view scroll-y class="lk-database-main-scroll">
            <!-- item -->
            <LkDatabaseItem
              ref="databaseItemRef"
              :layoutType="layoutType"
              :isPopup="true"
              :pathList="pathList"
              :bizType="bizType"
              @updatePathData="updatePathData"
              @updateSelectedItems="updateSelectedItems"
              :isEditing="layoutType === 2 || layoutType === 3"
              :preSelectedIds="preSelectedIds"
            />
          </scroll-view>
          <view v-if="layoutType === 2" class="saveToSpaceWrap">
            <!-- 点击btn1全选 -->
            <view class="btn1" @click="handleSelectAll">
              <up-checkbox-group>
                <up-checkbox
                  :checked="isSelectAll"
                  activeColor="#6d51f6"
                  :customStyle="{}"
                  shape="circle"
                  @change="handleSelectAll"
                >
                </up-checkbox>
              </up-checkbox-group>
              <view class="txt">全选</view>
              <view class="txt2">（已选 {{ selectedCount }}）</view>
            </view>
            <LkButton type="primary" shape="round" class="btn2" @click="saveToSpace"
              >确认上传</LkButton
            >
          </view>
          <LkButton
            v-else-if="layoutType === 3"
            type="primary"
            shape="round"
            class="saveBtn"
            @click="handleSave"
            >确认添加</LkButton
          >
          <LkButton v-else type="primary" shape="round" class="saveBtn" @click="clickSave"
            >保存当前位置</LkButton
          >
        </view>
      </view>
    </up-popup>
  </view>
</template>
<style lang="scss" scoped>
.lk-database {
  ::v-deep .u-popup__content {
    padding: 16px;
    padding-bottom: 14px;
    border-radius: 12px 12px 0px 0px;
    z-index: 9999;
  }
}
.wrapMain {
  min-height: 60vh;
}
.lk-database-header {
  display: flex;
  justify-content: center;
  align-items: center;
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #1d2129;
  }
  .close-btn {
    position: absolute;
    right: 14px;
    top: 16px;
  }
}
.lk-database-main {
  display: flex;
  flex-direction: column;
  .lk-database-main-scroll {
    height: 100%;
    width: 100%;
    height: 40vh;
    min-height: 40vh;
    max-height: 40vh;
    margin-bottom: 40px;
  }
  .saveToSpaceWrap {
    display: flex;
    justify-content: center;
    align-items: center;
    > .btn1,
    > .btn2 {
      flex: 1;
    }
    .btn1 {
      border-radius: 100px;
      border: 1px solid #dcdcdc;
      background: #fff;
      margin-right: 5.5px;
      height: 42px;
      padding: 0px 20px;
      display: flex;
      align-items: center;
      .u-checkbox {
        margin: 0;
      }
      .txt {
        color: #1d2129;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        margin-left: 4px;
        white-space: nowrap;
      }
      .txt2 {
        color: #1d2129;
        font-size: 12px;
        white-space: nowrap;
      }
    }
    .btn2 {
      margin-left: 5.5px;
      font-size: 16px;
      font-weight: 600;
      padding: 0 20px;
    }
  }
  .saveBtn {
    width: 100%;
    margin: 14px 0;
    flex-shrink: 0;
  }
}
.lk-database-search {
  margin: 24rpx 0;
}
</style>
