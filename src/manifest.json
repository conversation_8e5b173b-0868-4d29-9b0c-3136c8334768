{
  "name": "华云天图",
  "appid": "__UNI__3402EE1",
  "description": "",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  /* 5+App特有相关 */
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": false,
      "autoclose": true,
      "delay": 0
    },
    /* 模块配置 */
    "modules": {
      "OAuth": {},
      "Record": {},
      "Share": {},
      "Camera": {}
    },
    /* 应用发布信息 */
    "distribute": {
      /* android打包配置 */
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.INTERNET\"/>"
        ]
      },
      /* ios打包配置 */
      "ios": {
        "dSYMs": false,
        "capabilities": {
          "entitlements": {
            "com.apple.developer.associated-domains": [
              "applinks:static-mp-ec38e661-0866-4dfa-ba87-f35096fd5070.next.bspapp.com"
            ]
          }
        },
        "privacyDescription": {
          "NSPhotoLibraryUsageDescription": "测试读取相册",
          "NSPhotoLibraryAddUsageDescription": "测试写入相册",
          "NSCameraUsageDescription": "测试获取摄像头",
          "NSMicrophoneUsageDescription": "测试获取麦克风",
          "NSSpeechRecognitionUsageDescription": "测试获取语音",
          "NSAppleMusicUsageDescription": "测试获取媒体库",
          "NSLocalNetworkUsageDescription": "测试获取本地网络",
          "NSLocationWhenInUseUsageDescription": "测试允许期间访问位置",
          "NSContactsUsageDescription": "测试获取通讯录"
        }
      },
      /* SDK配置 */
      "sdkConfigs": {
        "oauth": {
          "weixin": {
            "appid": "wxf755e7341f2ce6b2",
            "appsecret": "ce71201c030e7a9c3a112dff77c30891",
            "UniversalLinks": "https://gpt-pre.hwzxs.com/"
          }
        },
        "share": {
          "weixin": {
            "appid": "wxf755e7341f2ce6b2",
            "UniversalLinks": "https://gpt-pre.hwzxs.com/"
          }
        }
      },
      "icons": {
        "android": {
          "hdpi": "unpackage/res/icons/72x72.png",
          "xhdpi": "unpackage/res/icons/96x96.png",
          "xxhdpi": "unpackage/res/icons/144x144.png",
          "xxxhdpi": "unpackage/res/icons/192x192.png"
        },
        "ios": {
          "appstore": "unpackage/res/icons/1024x1024.png",
          "ipad": {
            "app": "unpackage/res/icons/76x76.png",
            "app@2x": "unpackage/res/icons/152x152.png",
            "notification": "unpackage/res/icons/20x20.png",
            "notification@2x": "unpackage/res/icons/40x40.png",
            "proapp@2x": "unpackage/res/icons/167x167.png",
            "settings": "unpackage/res/icons/29x29.png",
            "settings@2x": "unpackage/res/icons/58x58.png",
            "spotlight": "unpackage/res/icons/40x40.png",
            "spotlight@2x": "unpackage/res/icons/80x80.png"
          },
          "iphone": {
            "app@2x": "unpackage/res/icons/120x120.png",
            "app@3x": "unpackage/res/icons/180x180.png",
            "notification@2x": "unpackage/res/icons/40x40.png",
            "notification@3x": "unpackage/res/icons/60x60.png",
            "settings@2x": "unpackage/res/icons/58x58.png",
            "settings@3x": "unpackage/res/icons/87x87.png",
            "spotlight@2x": "unpackage/res/icons/80x80.png",
            "spotlight@3x": "unpackage/res/icons/120x120.png"
          }
        }
      },
      "splashscreen": {
        "androidStyle": "common",
        "iosStyle": "common"
      }
    }
  },
  /* 快应用特有相关 */
  "quickapp": {},
  /* 小程序特有相关 */
  "mp-weixin": {
    "appid": "",
    "setting": {
      "urlCheck": false
    },
    "usingComponents": true
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {
    "usingComponents": true
  },
  "uniStatistics": {
    "enable": false
  },
  "vueVersion": "3",
  "_spaceID": "mp-ec38e661-0866-4dfa-ba87-f35096fd5070",
  "h5": {
    "devServer": {
      "https": true
    }
  }
}
