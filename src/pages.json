{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "华云天图",
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/app-center/index",
      "style": {
        "navigationBarTitleText": "应用中心",
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/data-space/index"
    },
    {
      "path": "pages/my/index",
      "style": {
        "navigationBarTitleText": "个人",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/search/index",
      "style": {
        "navigationBarTitleText": "搜索",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/chat/index",
      "style": {
        "navigationBarTitleText": "对话",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false,
        "enableScroll": false,
        "bounce": false,
        "disableSwipeBack": true,
        "app-plus": {
          "popGesture": "none"
        },
        "softInputMode": "adjustResize",
        "softInputNavBar": "none"
      }
    },
    {
      "path": "pages/theme-demo/index",
      "style": {
        "navigationBarTitleText": "主题样式演示",
        "navigationStyle": "default"
      }
    }
  ],
  "subpackages": [
    {
      "root": "pages-subpackages/auth-pkg",
      "pages": [
        {
          "path": "login/index",
          "style": {
            "navigationBarTitleText": "登录",
            "navigationStyle": "custom"
          }
        },

        {
          "path": "forgot-password/index",
          "style": {
            "navigationBarTitleText": "找回密码",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages-subpackages/app-center-pkg",
      "pages": [
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "应用详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "store/index",
          "style": {
            "navigationBarTitleText": "应用商店",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "solutions/index",
          "style": {
            "navigationBarTitleText": "解决方案",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "create-app/index",
          "style": {
            "navigationBarTitleText": "AI创建应用",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "create-app-success/index",
          "style": {
            "navigationBarTitleText": "应用创建成功",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "edit-app/index",
          "style": {
            "navigationBarTitleText": "编辑应用",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages-subpackages/data-space-pkg",
      "pages": [
        {
          "path": "sub-space/index",
          "style": {
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages-subpackages/chat-pkg",
      "pages": [
        {
          "path": "share/index",
          "style": {
            "navigationBarTitleText": "分享",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages-subpackages/my-pkg",
      "pages": [
        {
          "path": "history/index",
          "style": {
            "navigationBarTitleText": "历史记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "recycleBin/index",
          "style": {
            "navigationBarTitleText": "回收站",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "messages/index",
          "style": {
            "navigationBarTitleText": "消息通知",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "about/index",
          "style": {
            "navigationBarTitleText": "关于我们",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "feedback/feedback",
          "style": {
            "navigationBarTitleText": "意见反馈",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "feedback/feedDetail",
          "style": {
            "navigationBarTitleText": "反馈详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting/index",
          "style": {
            "navigationBarTitleText": "设置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "user-agreement/index",
          "style": {
            "navigationBarTitleText": "用户协议和隐私政策",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "modify/index",
          "style": {
            "navigationBarTitleText": "个人信息",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "华云天图",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "rpxCalcMaxDeviceWidth": 1024, // rpx 计算所支持的最大设备宽度，单位 px，默认值为 960
    "rpxCalcBaseDeviceWidth": 375, // rpx 计算使用的基准设备宽度，设备实际宽度超出 rpx 计算所支持的最大设备宽度时将按基准宽度计算，单位 px，默认值为 375
    "rpxCalcIncludeWidth": 750 // rpx 计算特殊处理的值，始终按实际的设备宽度计算，单位 rpx，默认值为 750
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  }
}
